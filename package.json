{"name": "code-test-platform", "version": "1.0.0", "description": "Code test platform with frontend and backend", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev:frontend": "npm run dev --workspace=frontend", "dev:backend": "npm run dev --workspace=backend", "dev": "npm-run-all --parallel dev:*", "build": "npm run build --workspaces", "lint": "npm run lint --workspaces", "format": "prettier --write ."}, "devDependencies": {"npm-run-all": "^4.1.5", "prettier": "^3.2.5"}, "dependencies": {"monaco-editor": "^0.52.2", "react-resizable-panels": "^3.0.1", "vite-plugin-monaco-editor": "^1.1.0"}}