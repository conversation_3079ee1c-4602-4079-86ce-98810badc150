/* eslint-disable react-refresh/only-export-components */
import { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import { Building2 } from 'lucide-react';

interface OrgContextType {
  orgName: string;
  orgIcon: ReactNode;
  setOrgName: (name: string) => void;
}

const OrgContext = createContext<OrgContextType | undefined>(undefined);

export function OrgProvider({ children }: { children: ReactNode }) {
  const [orgName, setOrgName] = useState('Code Test Platform');

  // Default organization icon
  const orgIcon = (
    <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
      <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
    </div>
  );

  // In a real application, you would fetch the organization details from your backend
  useEffect(() => {
    // Example: Fetch organization details
    // const fetchOrgDetails = async () => {
    //   try {
    //     const response = await fetch('/api/org/details');
    //     const data = await response.json();
    //     setOrgName(data.name);
    //   } catch (error) {
    //     console.error('Failed to fetch organization details:', error);
    //   }
    // };
    // fetchOrgDetails();
  }, []);

  return (
    <OrgContext.Provider value={{ orgName, orgIcon, setOrgName }}>
      {children}
    </OrgContext.Provider>
  );
}

export function useOrg() {
  const context = useContext(OrgContext);
  if (context === undefined) {
    throw new Error('useOrg must be used within an OrgProvider');
  }
  return context;
}
