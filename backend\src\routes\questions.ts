// backend/src/routes/questions.ts

import express from 'express';
import {
  createQuestion,
  getAllQuestions,
  getQuestionById,
  updateQuestion,
  deleteQuestion,
  getQuestionsByCategory,
  getQuestionsByDifficulty
} from '../controllers/questionsController';

const router = express.Router();

// Question CRUD routes
router.post('/', createQuestion);                    // POST /api/questions
router.get('/', getAllQuestions);                    // GET /api/questions
router.get('/:id', getQuestionById);                 // GET /api/questions/:id
router.put('/:id', updateQuestion);                  // PUT /api/questions/:id
router.delete('/:id', deleteQuestion);               // DELETE /api/questions/:id

// Additional query routes
router.get('/category/:category', getQuestionsByCategory);      // GET /api/questions/category/:category
router.get('/difficulty/:difficulty', getQuestionsByDifficulty); // GET /api/questions/difficulty/:difficulty

export default router;
