import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Home from './pages/Home';
import About from './pages/About';
import NotFound from './pages/NotFound';
import { ThemeProvider } from './components/ui/theme-provider';
import { OrgProvider } from './context/OrgContext';
import { AuthProvider, useAuth } from './context/AuthContext';
import Login from './pages/Login';
import Authenticate from './pages/auth/authenticate';
import Dashboard from './pages/Dashboard';
import StartCoding from './pages/StartCoding';
import { StytchProvider } from '@stytch/react';
import { StytchHeadlessClient } from '@stytch/vanilla-js/headless';
import Onboarding from './pages/auth/Onboarding';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import AdminDashboard from './pages/dashboard/AdminDashboard';
// Create a client
const queryClient = new QueryClient();

const stytchOptions = {
  cookieOptions: {
    opaqueTokenCookieName: 'stytch_session',
    jwtCookieName: 'stytch_session_jwt',
    path: '',
    availableToSubdomains: true,
    domain: '',
  },
};

const stytchClient = new StytchHeadlessClient(
  'public-token-test-bcc3e3de-5e59-4f60-a7f0-d0b78aed4f3a',
  stytchOptions
);

// Protected route component - can be used to wrap routes that require authentication
// Currently using the AuthenticatedRoutes component for this purpose
// Keeping this here for reference in case we need it in the future
/*
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  return <>{children}</>;
};
*/

// Wrapper component that handles auth-dependent routing
function AuthenticatedRoutes() {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Debug logging
  useEffect(() => {
    console.log('AuthenticatedRoutes - Auth state:', {
      isAuthenticated,
      isLoading,
      user,
      path: window.location.pathname
    });

    if (user) {
      // Check if metadata exists and if onboarding is complete
      let onboardingStatus = user.onboardingComplete;

      if (user.metadata && typeof user.metadata === 'string') {
        try {
          const metadata = JSON.parse(user.metadata);
          if (metadata.onboardingComplete !== undefined) {
            onboardingStatus = metadata.onboardingComplete;
          }
        } catch (e) {
          console.error('Error parsing user metadata:', e);
        }
      }

      console.log('User onboarding status:', onboardingStatus);
    }
  }, [user, isAuthenticated, isLoading]);

  // While checking authentication status, show an animated loading screen
  if (isLoading) {
    return (
      <div className="h-screen w-full flex flex-col items-center justify-center bg-background">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center gap-4"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
          >
            <Loader2 className="h-12 w-12 text-primary" />
          </motion.div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="text-lg font-medium text-muted-foreground"
          >
            Verifying your session...
          </motion.p>
        </motion.div>
      </div>
    );
  }

  // If user is authenticated but hasn't completed onboarding, redirect to onboarding
  if (
    isAuthenticated &&
    user &&
    !isLoading &&
    window.location.pathname !== '/onboarding' &&
    window.location.pathname !== '/authenticate'
  ) {
    console.log('Checking user status for redirection:', { user });

    // Check if onboarding is complete from user object or metadata
    let onboardingComplete = user.onboardingComplete || false;

    // Try to parse metadata if it exists
    if (user.metadata) {
      try {
        const metadata = typeof user.metadata === 'string'
          ? JSON.parse(user.metadata)
          : user.metadata;

        console.log('Parsed metadata for redirection check:', metadata);

        if (metadata && metadata.onboardingComplete === true) {
          onboardingComplete = true;
        }
      } catch (error) {
        console.error('Error parsing metadata in redirect check:', error);
      }
    }

    if (!onboardingComplete) {
      console.log('User needs to complete onboarding, redirecting to /onboarding');
      return <Navigate to="/onboarding" />;
    }

    // Role-based redirection for completed onboarding users
    // Only redirect if they're trying to access a generic route
    const isGenericRoute =
      window.location.pathname === '/' ||
      window.location.pathname === '/dashboard';

    if (isGenericRoute) {
      const userRole = user.role || 'student'; // Default to student if no role

      if (userRole === 'admin') {
        console.log('Admin user detected, redirecting to admin dashboard');
        return <Navigate to="/admin" />;
      } else {
        console.log('Student user detected, redirecting to dashboard');
        return <Navigate to="/dashboard" />;
      }
    }
  }

  // If user is not authenticated and trying to access protected routes, redirect to login
  if (
    !isAuthenticated &&
    window.location.pathname !== '/auth' &&
    window.location.pathname !== '/authenticate'
  ) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/auth" element={<Login />} />
      <Route path="/authenticate" element={<Authenticate />} />
      <Route path="/onboarding" element={<Onboarding />} />
      <Route path="/about" element={<About />} />
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/start-coding" element={<StartCoding />} />
      <Route path="/admin" element={<AdminDashboard />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <StytchProvider stytch={stytchClient}>
        <ThemeProvider>
          <OrgProvider>
            <Router>
              <AuthProvider>
                <AuthenticatedRoutes />
              </AuthProvider>
            </Router>
          </OrgProvider>
        </ThemeProvider>
      </StytchProvider>
    </QueryClientProvider>
  );
};

export default App;
