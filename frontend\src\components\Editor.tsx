import { useEffect, useRef, useState, useCallback } from 'react';
import * as monaco from 'monaco-editor';
import { But<PERSON> } from '../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { languages, getLanguageTemplate, getLanguageInfo } from '@/lib/languages';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '../components/ui/resizable';
import { Loader2, Info, Play, Pause, Clock, XCircle, CheckCircle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../components/ui/tooltip";
import { Textarea } from "../components/ui/textarea";

import { Label } from "../components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../components/ui/tabs";

interface TestCaseResult {
  passed: boolean;
  input: string;
  expected: string;
  actual: string;
}

interface CompilationResult {
  success: boolean;
  results?: {
    passed: number;
    total: number;
    testCases: TestCaseResult[];
    executionTime?: string;
    memoryUsed?: number;
    failedTestCase?: {
      index: number;
      input: string;
      expected: string;
      actual: string;
    };
  };
  error?: {
    type: string;
    message: string;
    details?: string;
  };
  message?: string;
}



interface EditorProps {
  onCompile: (code: string, language: string) => Promise<CompilationResult>;
  onSubmit: (code: string, language: string) => Promise<CompilationResult>;
}

const editorThemes = [
  { id: 'vs', name: 'Light' },
  { id: 'vs-dark', name: 'Dark' },
  { id: 'hc-black', name: 'High Contrast' },
];

// Language IDs are handled by the backend

export function Editor({ onCompile, onSubmit }: EditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [editor, setEditor] = useState<monaco.editor.IStandaloneCodeEditor | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0].id);
  const [selectedTheme, setSelectedTheme] = useState('vs');
  const [isLoading, setIsLoading] = useState(false);
  const [compilationResult, setCompilationResult] = useState<CompilationResult | null>(null);

  // Custom test case state
  const [customTestInput, setCustomTestInput] = useState<string>('nums = [1,2,3], target = 5');
  const [customTestExpectedOutput, setCustomTestExpectedOutput] = useState<string>('');
  const [isCustomTestLoading, setIsCustomTestLoading] = useState(false);
  const [customTestResult, setCustomTestResult] = useState<CompilationResult | null>(null);
  const [activeTab, setActiveTab] = useState<string>('standard');

  // Timer state
  const [time, setTime] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [hasUserStartedCoding, setHasUserStartedCoding] = useState(false);
  const [hasUserSubmitted, setHasUserSubmitted] = useState(false);

  // Timer functions
  const startTimer = useCallback(() => {
    if (!isTimerRunning) {
      setIsTimerRunning(true);
      timerRef.current = setInterval(() => {
        setTime(prevTime => prevTime + 1);
      }, 1000);
    }
  }, [isTimerRunning]);

  const pauseTimer = useCallback(() => {
    if (isTimerRunning && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setIsTimerRunning(false);
    }
  }, [isTimerRunning]);

  const resetTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setTime(0);
    setIsTimerRunning(false);
  }, []);

  const toggleTimer = useCallback(() => {
    if (isTimerRunning) {
      pauseTimer();
    } else {
      startTimer();
    }
  }, [isTimerRunning, pauseTimer, startTimer]);

  // Format time as mm:ss
  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Initialize Monaco Editor
  useEffect(() => {
    if (editorRef.current) {
      const editorInstance = monaco.editor.create(editorRef.current, {
        value: getLanguageTemplate(selectedLanguage),
        language: selectedLanguage,
        theme: selectedTheme,
        automaticLayout: true,
        minimap: { enabled: false },
        fontSize: 14,
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        readOnly: false,
        cursorStyle: 'line',
        tabSize: 2,
      });

      // Set up read-only regions
      const model = editorInstance.getModel();
      if (model) {
        const templateLines = languages.find(lang => lang.id === selectedLanguage)?.template.split('\n').length || 0;
        const driverCodeStart = templateLines + 2;

        // Apply decorations to driver code section
        editorInstance.createDecorationsCollection([
          {
            range: new monaco.Range(driverCodeStart, 1, model.getLineCount(), 1),
            options: {
              isWholeLine: true,
              className: 'readOnlyRegion',
              glyphMarginClassName: 'readOnlyRegion',
            },
          },
        ]);

        model.onDidChangeContent((e) => {
          const changes = e.changes;
          for (const change of changes) {
            if (change.range.startLineNumber >= driverCodeStart) {
              const originalContent = getLanguageTemplate(selectedLanguage);
              const lines = originalContent.split('\n');
              const driverCode = lines.slice(templateLines + 2).join('\n');
              const currentContent = model.getValue();
              const userCode = currentContent.split('\n').slice(0, templateLines).join('\n');

              model.pushEditOperations(
                [],
                [
                  {
                    range: new monaco.Range(1, 1, model.getLineCount(), 1),
                    text: `${userCode}\n\n${driverCode}`,
                  },
                ],
                () => null
              );
              break;
            }
          }
        });
      }

      // Add event listener to start timer when user first clicks on the editor
      editorInstance.onMouseDown(() => {
        if (!hasUserStartedCoding && !isTimerRunning) {
          setHasUserStartedCoding(true);
          startTimer();
        }
      });

      setEditor(editorInstance);

      return () => {
        editorInstance.dispose();
      };
    }
  }, [hasUserStartedCoding, isTimerRunning, startTimer]);

  // Update editor language and template when language changes
  useEffect(() => {
    if (editor) {
      monaco.editor.setModelLanguage(editor.getModel()!, selectedLanguage);
      editor.setValue(getLanguageTemplate(selectedLanguage));
    }
  }, [selectedLanguage, editor]);

  // Update editor theme when theme changes
  useEffect(() => {
    if (editor) {
      monaco.editor.setTheme(selectedTheme);
    }
  }, [selectedTheme, editor]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const handleLanguageChange = (value: string) => {
    setSelectedLanguage(value);
    setCompilationResult(null);
  };

  const handleThemeChange = (value: string) => {
    setSelectedTheme(value);
  };

  const handleCompile = async () => {
    if (editor) {
      setIsLoading(true);
      setCompilationResult(null);
      try {
        const code = editor.getValue();
        // Format code before sending
        const formattedCode = code.replace(/\r\n/g, '\n').trim();
        const result = await onCompile(formattedCode, selectedLanguage);
        setCompilationResult(result);
      } catch (error) {
        setCompilationResult({
          success: false,
          error: {
            type: 'API_ERROR',
            message: 'Failed to compile code',
            details: error instanceof Error ? error.message : 'Unknown error',
          },
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleSubmit = async () => {
    if (editor) {
      setIsLoading(true);
      setCompilationResult(null);

      // Stop the timer if this is the first submission
      if (!hasUserSubmitted && isTimerRunning) {
        pauseTimer();
        setHasUserSubmitted(true);
      }

      try {
        const code = editor.getValue();
        // Format code before sending
        const formattedCode = code.replace(/\r\n/g, '\n').trim();
        const result = await onSubmit(formattedCode, selectedLanguage);
        setCompilationResult(result);
      } catch (error) {
        setCompilationResult({
          success: false,
          error: {
            type: 'API_ERROR',
            message: 'Failed to submit code',
            details: error instanceof Error ? error.message : 'Unknown error',
          },
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCustomTest = async () => {
    if (editor && customTestInput.trim()) {
      setIsCustomTestLoading(true);
      setCustomTestResult(null);

      // Validate the custom input format
      const isValidInputFormat = validateCustomInput(customTestInput);
      const isValidExpectedOutput = validateExpectedOutput(customTestExpectedOutput.trim());

      if (!isValidInputFormat) {
        setCustomTestResult({
          success: false,
          error: {
            type: 'VALIDATION_ERROR',
            message: 'Invalid input format',
            details: 'Custom input must be in the format: nums = [1,2,3], target = 9',
          },
        });
        setIsCustomTestLoading(false);
        return;
      }

      if (customTestExpectedOutput.trim() && !isValidExpectedOutput) {
        setCustomTestResult({
          success: false,
          error: {
            type: 'VALIDATION_ERROR',
            message: 'Invalid expected output format',
            details: 'Expected output must be in the format: [0,1] or [1,2]',
          },
        });
        setIsCustomTestLoading(false);
        return;
      }

      try {
        const code = editor.getValue();
        // Format code before sending
        const formattedCode = code.replace(/\r\n/g, '\n').trim();

        const response = await fetch('http://localhost:3001/api/coding/custom-test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code: formattedCode,
            language: selectedLanguage,
            customInput: customTestInput,
            expectedOutput: customTestExpectedOutput.trim() || null
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to run custom test');
        }

        const result = await response.json();
        setCustomTestResult(result);
        setActiveTab('custom');
      } catch (error) {
        setCustomTestResult({
          success: false,
          error: {
            type: 'API_ERROR',
            message: 'Failed to run custom test',
            details: error instanceof Error ? error.message : 'Unknown error',
          },
        });
      } finally {
        setIsCustomTestLoading(false);
      }
    }
  };

  // Function to validate custom input format
  const validateCustomInput = (input: string): boolean => {
    // Check if the input matches the expected format: nums = [1,2,3], target = 9
    const numsMatch = input.match(/nums\s*=\s*\[(.*?)\]/);
    const targetMatch = input.match(/target\s*=\s*(\d+)/);

    if (!numsMatch || !targetMatch) {
      return false;
    }

    // Validate that the nums array contains valid numbers
    const numsStr = numsMatch[1];
    const nums = numsStr.split(',').map(n => n.trim());

    for (const num of nums) {
      if (!/^-?\d+$/.test(num)) {
        return false;
      }
    }

    return true;
  };

  // Function to validate expected output format
  const validateExpectedOutput = (output: string): boolean => {
    if (!output) return true; // Empty output is valid (optional field)

    try {
      // Try to parse as JSON
      const jsonStr = output.replace(/'/g, '"');
      const parsed = JSON.parse(jsonStr);

      // Check if it's an array of exactly 2 numbers
      if (!Array.isArray(parsed) || parsed.length !== 2) {
        return false;
      }

      // Check if both elements are valid numbers
      return parsed.every(num => Number.isInteger(num) && num >= 0);
    } catch (e) {
      return false;
    }
  };

  return (
    <Card className="w-full h-full flex flex-col overflow-hidden border-0 rounded-none shadow-none">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 flex-shrink-0 border-b">
        <div className="flex items-center space-x-3">
          <CardTitle>Code Editor</CardTitle>
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              className={`timer-button ${isTimerRunning ? 'timer-running' : ''} ${hasUserSubmitted ? 'opacity-70' : ''}`}
              onClick={toggleTimer}
              disabled={hasUserSubmitted}
              title={hasUserSubmitted ? "Timer stopped after submission" : "Start/Pause timer"}
            >
              {isTimerRunning ? (
                <Pause className="h-4 w-4 mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              <span className="timer-display">{formatTime(time)}</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="timer-reset-button"
              onClick={resetTimer}
              disabled={hasUserSubmitted}
              title={hasUserSubmitted ? "Timer locked after submission" : "Reset Timer"}
            >
              <Clock className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedLanguage} onValueChange={handleLanguageChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent className="max-h-[300px] overflow-y-auto scrollable-dropdown">
              {languages.map((language) => {
                const langInfo = getLanguageInfo(language.id);
                return (
                  <SelectItem
                    key={language.id}
                    value={language.id}
                    className="language-item"
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{language.name}</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="inline-flex cursor-help ml-4">
                              <Info size={14} className="text-muted-foreground" />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right" align="start" className="p-4">
                            <div className="space-y-2">
                              <h4 className="font-medium">{language.name}</h4>
                              {langInfo.compiler && (
                                <div>
                                  <span className="text-xs font-medium text-muted-foreground">Compiler:</span>
                                  <p className="text-sm">{langInfo.compiler} {langInfo.version}</p>
                                </div>
                              )}
                              {langInfo.standardLibraries && langInfo.standardLibraries.length > 0 && (
                                <div>
                                  <span className="text-xs font-medium text-muted-foreground">Standard Libraries:</span>
                                  <ul className="text-sm list-disc list-inside">
                                    {langInfo.standardLibraries.map((lib, i) => (
                                      <li key={i}>{lib}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              {langInfo.features && langInfo.features.length > 0 && (
                                <div>
                                  <span className="text-xs font-medium text-muted-foreground">Features:</span>
                                  <ul className="text-sm list-disc list-inside">
                                    {langInfo.features.map((feature, i) => (
                                      <li key={i}>{feature}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              {langInfo.notes && langInfo.notes.length > 0 && (
                                <div>
                                  <span className="text-xs font-medium text-muted-foreground">Notes:</span>
                                  <ul className="text-sm list-disc list-inside">
                                    {langInfo.notes.map((note, i) => (
                                      <li key={i}>{note}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          <Select value={selectedTheme} onValueChange={handleThemeChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select theme" />
            </SelectTrigger>
            <SelectContent>
              {editorThemes.map((theme) => (
                <SelectItem key={theme.id} value={theme.id}>
                  {theme.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col relative p-0">
        <ResizablePanelGroup direction="vertical" className="flex-1">
          {/* Code editor panel - can be resized by dragging the handle */}
          <ResizablePanel defaultSize={75} minSize={50}>
            <div ref={editorRef} className="h-full w-full" />
          </ResizablePanel>

          {/* Resizable handle with visible grip */}
          <ResizableHandle withHandle className="bg-muted" />

          {/* Results panel - contains test results and buttons */}
          <ResizablePanel defaultSize={25} minSize={15}>
            <div className="h-full flex flex-col relative overflow-hidden">
              {/* Tabs for standard tests and custom test */}
              <Tabs
                defaultValue="standard"
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full h-full flex flex-col"
              >
                <div className="flex items-center justify-between px-4 pt-2">
                  <TabsList>
                    <TabsTrigger value="standard">Standard Tests</TabsTrigger>
                    <TabsTrigger value="custom">Custom Test</TabsTrigger>
                  </TabsList>
                </div>

                {/* Standard test cases tab */}
                <TabsContent value="standard" className="flex-1 h-full overflow-hidden mt-0">
                  {compilationResult && (
                    <div className="p-4 h-full overflow-y-auto pb-16">
                      {/* Display raw JSON for debugging - uncomment to debug */}
                      {/* <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded mb-4 overflow-auto max-h-40">
                        {JSON.stringify(compilationResult, null, 2)}
                      </pre> */}

                      {compilationResult.success ? (
                        <div>
                          <div className="flex items-center gap-2 mb-4">
                            <div className={`text-sm font-medium px-3 py-1 rounded-full ${
                              compilationResult.results?.passed === compilationResult.results?.total
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                            }`}>
                              {compilationResult.results?.passed === compilationResult.results?.total
                                ? "Accepted"
                                : "Wrong Answer"}
                            </div>
                            <span className="text-sm text-muted-foreground">
                              {compilationResult.results?.passed}/{compilationResult.results?.total} test cases passed
                            </span>
                          </div>

                          {/* Execution metrics */}
                          {compilationResult.results?.executionTime && (
                            <div className="flex flex-col md:flex-row gap-4 mb-4 p-3 bg-muted rounded-md">
                              <div className="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                                  <circle cx="12" cy="12" r="10"/>
                                  <polyline points="12 6 12 12 16 14"/>
                                </svg>
                                <div>
                                  <span className="text-sm font-medium">Runtime</span>
                                  <p className="text-sm text-muted-foreground">{compilationResult.results.executionTime}</p>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-500">
                                  <path d="M18 18V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v12"/>
                                  <path d="M2 18h20"/>
                                  <path d="M14 18v-4h-4v4"/>
                                </svg>
                                <div>
                                  <span className="text-sm font-medium">Memory</span>
                                  <p className="text-sm text-muted-foreground">{compilationResult.results.memoryUsed ? `${compilationResult.results.memoryUsed} KB` : 'N/A'}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Message from the server - User-friendly version */}
                          {compilationResult.message && (
                            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-900/50 rounded-md">
                              <p className="text-blue-700 dark:text-blue-300">
                                {(() => {
                                  // Create a more user-friendly message
                                  if (compilationResult.message.includes("Failed at test case") && compilationResult.results?.failedTestCase) {
                                    const testCaseMatch = compilationResult.message.match(/Failed at test case (\d+)/);
                                    const testCaseNum = testCaseMatch ? testCaseMatch[1] : "1";

                                    try {
                                      const inputObj = JSON.parse(compilationResult.results.failedTestCase.input);
                                      const expected = compilationResult.results.failedTestCase.expected;
                                      const actual = compilationResult.results.failedTestCase.actual;

                                      return `Your solution failed on test case ${testCaseNum}. For input nums = ${JSON.stringify(inputObj.nums)}, target = ${inputObj.target}, your code returned ${actual} instead of the expected ${expected}.`;
                                    } catch (e) {
                                      return compilationResult.message;
                                    }
                                  } else if (compilationResult.message.includes("Runtime error")) {
                                    return "Your code encountered a runtime error. Check for exceptions, null pointers, or array index out of bounds issues.";
                                  } else if (compilationResult.message.includes("Compilation error")) {
                                    return "Your code failed to compile. Check for syntax errors or missing semicolons.";
                                  } else {
                                    return compilationResult.message;
                                  }
                                })()}
                              </p>
                            </div>
                          )}

                          <div className="space-y-4">
                            {/* Display sample test cases first */}
                            {compilationResult.results?.testCases && compilationResult.results.testCases.length > 0 && (
                              <div className="mb-4">
                                <h3 className="text-lg font-medium mb-2">Sample Test Cases</h3>
                                <div className="space-y-2">
                                  {compilationResult.results.testCases.map((testCase, index) => (
                                    <div
                                      key={`sample-${index}`}
                                      className={`border rounded-md overflow-hidden ${
                                        testCase.passed
                                          ? "border-green-200 dark:border-green-900/50"
                                          : "border-red-200 dark:border-red-900/50"
                                      }`}
                                    >
                                      <div
                                        className={`flex items-center justify-between px-4 py-2 cursor-pointer ${
                                          testCase.passed
                                            ? "bg-green-50 dark:bg-green-900/20"
                                            : "bg-red-50 dark:bg-red-900/20"
                                        }`}
                                        onClick={() => {
                                          // Toggle visibility of test case details
                                          const details = document.getElementById(`sample-test-details-${index}`);
                                          if (details) {
                                            details.classList.toggle('hidden');
                                          }
                                        }}
                                      >
                                        <div className="flex items-center gap-2">
                                          <span className="font-medium">
                                            {testCase.passed ? "✓" : "✗"} Test Case {index + 1}
                                          </span>
                                        </div>
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="16"
                                          height="16"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          stroke="currentColor"
                                          strokeWidth="2"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          className="chevron-down"
                                        >
                                          <path d="m6 9 6 6 6-6"/>
                                        </svg>
                                      </div>

                                      <div id={`sample-test-details-${index}`} className="px-4 py-3 font-mono text-sm border-t overflow-auto max-h-60 block">
                                        <div className="grid grid-cols-1 gap-2">
                                          <div>
                                            <span className="text-gray-500 dark:text-gray-400">Input:</span>
                                            <div className="mt-1 bg-muted p-2 rounded overflow-auto">
                                              {/* Try to parse and format the JSON input */}
                                              {(() => {
                                                try {
                                                  const inputObj = JSON.parse(testCase.input);
                                                  return `nums = ${JSON.stringify(inputObj.nums)}, target = ${inputObj.target}`;
                                                } catch (e) {
                                                  return testCase.input;
                                                }
                                              })()}
                                            </div>
                                          </div>
                                          <div>
                                            <span className="text-gray-500 dark:text-gray-400">Expected Output:</span>
                                            <div className="mt-1 bg-muted p-2 rounded overflow-auto">{testCase.expected}</div>
                                          </div>
                                          <div>
                                            <span className="text-gray-500 dark:text-gray-400">Your Output:</span>
                                            <div className={`mt-1 p-2 rounded overflow-auto ${
                                              testCase.passed
                                                ? "bg-green-50 dark:bg-green-900/20"
                                                : "bg-red-50 dark:bg-red-900/20"
                                            }`}>
                                              {testCase.actual.includes("Runtime error")
                                                ? "Runtime error occurred. Check your code for syntax errors or exceptions."
                                                : testCase.actual}
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Display failed test case for submission */}
                            {compilationResult.results?.failedTestCase && (
                              <div
                                className="border rounded-md overflow-hidden border-red-200 dark:border-red-900/50"
                              >
                                <div
                                  className="flex items-center justify-between px-4 py-2 cursor-pointer bg-red-50 dark:bg-red-900/20"
                                  onClick={() => {
                                    // Toggle visibility of test case details
                                    const details = document.getElementById(`failed-test-details`);
                                    if (details) {
                                      details.classList.toggle('hidden');
                                    }
                                  }}
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">
                                      ✗ Failed Test Case {compilationResult.results.failedTestCase.index}
                                    </span>
                                  </div>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="chevron-down"
                                  >
                                    <path d="m6 9 6 6 6-6"/>
                                  </svg>
                                </div>

                                <div id="failed-test-details" className="px-4 py-3 font-mono text-sm border-t overflow-auto max-h-60 block">
                                  <div className="grid grid-cols-1 gap-2">
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Input:</span>
                                      <div className="mt-1 bg-muted p-2 rounded overflow-auto">
                                        {/* Try to parse and format the JSON input */}
                                        {(() => {
                                          try {
                                            const inputObj = JSON.parse(compilationResult.results.failedTestCase.input);
                                            return `nums = ${JSON.stringify(inputObj.nums)}, target = ${inputObj.target}`;
                                          } catch (e) {
                                            return compilationResult.results.failedTestCase.input;
                                          }
                                        })()}
                                      </div>
                                    </div>
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Expected Output:</span>
                                      <div className="mt-1 bg-muted p-2 rounded overflow-auto">{compilationResult.results.failedTestCase.expected}</div>
                                    </div>
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Your Output:</span>
                                      <div className="mt-1 p-2 rounded overflow-auto bg-red-50 dark:bg-red-900/20">
                                        {compilationResult.results.failedTestCase.actual.includes("Runtime error")
                                          ? "Runtime error occurred. Check your code for syntax errors or exceptions."
                                          : compilationResult.results.failedTestCase.actual}
                                      </div>
                                    </div>

                                    {/* Add execution metrics */}
                                    {compilationResult.results.executionTime && (
                                      <div>
                                        <span className="text-gray-500 dark:text-gray-400">Execution Time:</span>
                                        <div className="mt-1 bg-muted p-2 rounded overflow-auto">
                                          {compilationResult.results.executionTime}
                                        </div>
                                      </div>
                                    )}

                                    {compilationResult.results.memoryUsed && (
                                      <div>
                                        <span className="text-gray-500 dark:text-gray-400">Memory Used:</span>
                                        <div className="mt-1 bg-muted p-2 rounded overflow-auto">
                                          {compilationResult.results.memoryUsed} KB
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Display regular test cases if available */}
                            {compilationResult.results?.testCases && compilationResult.results.testCases.length > 0 &&
                              compilationResult.results.testCases.map((testCase, index) => (
                              <div
                                key={index}
                                className={`border rounded-md overflow-hidden ${
                                  testCase.passed
                                    ? "border-green-200 dark:border-green-900/50"
                                    : "border-red-200 dark:border-red-900/50"
                                }`}
                              >
                                <div
                                  className={`flex items-center justify-between px-4 py-2 cursor-pointer ${
                                    testCase.passed
                                      ? "bg-green-50 dark:bg-green-900/20"
                                      : "bg-red-50 dark:bg-red-900/20"
                                  }`}
                                  onClick={() => {
                                    // Toggle visibility of test case details
                                    const details = document.getElementById(`test-details-${index}`);
                                    if (details) {
                                      details.classList.toggle('hidden');
                                    }
                                  }}
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">
                                      {testCase.passed ? "✓" : "✗"} Test Case {index + 1}
                                    </span>
                                  </div>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="chevron-down"
                                  >
                                    <path d="m6 9 6 6 6-6"/>
                                  </svg>
                                </div>

                                <div id={`test-details-${index}`} className="px-4 py-3 font-mono text-sm border-t overflow-auto max-h-60">
                                  <div className="grid grid-cols-1 gap-2">
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Input:</span>
                                      <div className="mt-1 bg-muted p-2 rounded overflow-auto">
                                        {(() => {
                                          try {
                                            // Try to parse and format the input
                                            if (testCase.input.includes('{')) {
                                              const inputObj = JSON.parse(testCase.input);
                                              return `nums = ${JSON.stringify(inputObj.nums)}, target = ${inputObj.target}`;
                                            }
                                            return testCase.input;
                                          } catch (e) {
                                            return testCase.input;
                                          }
                                        })()}
                                      </div>
                                    </div>
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Expected Output:</span>
                                      <div className="mt-1 bg-muted p-2 rounded overflow-auto">{testCase.expected}</div>
                                    </div>
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Your Output:</span>
                                      <div className={`mt-1 p-2 rounded overflow-auto ${
                                        testCase.passed
                                          ? "bg-green-50 dark:bg-green-900/20"
                                          : "bg-red-50 dark:bg-red-900/20"
                                      }`}>
                                        {testCase.actual}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900/50 rounded-md p-4 overflow-auto max-h-[calc(100vh-300px)]">
                          <p className="font-semibold text-red-700 dark:text-red-400">{compilationResult.error?.type}</p>
                          <p className="mt-1 text-red-600 dark:text-red-300">{compilationResult.error?.message}</p>
                          {compilationResult.error?.details && (
                            <div className="font-mono text-sm mt-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-md overflow-auto max-h-60">
                              <pre className="whitespace-pre-wrap">{compilationResult.error.details}</pre>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>

                {/* Custom test case tab */}
                <TabsContent value="custom" className="flex-1 h-full overflow-hidden mt-0">
                  <div className="p-4 h-full overflow-y-auto pb-16">
                    <div className="mb-4 space-y-4">
                      <div>
                        <Label htmlFor="customInput">Custom Input</Label>
                        <Textarea
                          id="customInput"
                          placeholder="Format: nums = [1,2,3], target = 9"
                          value={customTestInput}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setCustomTestInput(e.target.value)}
                          className="mt-1 font-mono"
                          rows={3}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Input must be in the format: <code className="bg-muted px-1 py-0.5 rounded">nums = [1,2,3], target = 9</code>
                        </p>
                      </div>
                      <div>
                        <Label htmlFor="expectedOutput">Expected Output (Optional)</Label>
                        <Textarea
                          id="expectedOutput"
                          placeholder="Format: [0,1] or [1,2]"
                          value={customTestExpectedOutput}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setCustomTestExpectedOutput(e.target.value)}
                          className="mt-1 font-mono"
                          rows={2}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Expected output must be an array of two indices, e.g. <code className="bg-muted px-1 py-0.5 rounded">[0,1]</code> or <code className="bg-muted px-1 py-0.5 rounded">[1,2]</code>
                        </p>
                      </div>
                      <Button
                        onClick={handleCustomTest}
                        disabled={isCustomTestLoading || !customTestInput.trim()}
                        className="w-full"
                      >
                        {isCustomTestLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Running...
                          </>
                        ) : (
                          'Run Custom Test'
                        )}
                      </Button>
                    </div>

                    {/* Custom test results */}
                    {customTestResult && customTestResult.success && (
                      <div className="mt-4">
                        <div className="space-y-2">
                          {customTestResult.results?.testCases.map((testCase, index) => (
                            <div
                              key={`custom-${index}`}
                              className={`border rounded-md overflow-hidden ${
                                testCase.passed
                                  ? "border-green-200 dark:border-green-900/50"
                                  : "border-red-200 dark:border-red-900/50"
                              }`}
                            >
                              <div
                                className={`flex items-center justify-between px-4 py-2 cursor-pointer ${
                                  testCase.passed
                                    ? "bg-green-50 dark:bg-green-900/20"
                                    : "bg-red-50 dark:bg-red-900/20"
                                }`}
                                onClick={() => {
                                  // Toggle visibility of test case details
                                  const details = document.getElementById(`custom-test-details-${index}`);
                                  if (details) {
                                    details.classList.toggle('hidden');
                                  }
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  <span className="font-medium">
                                    {testCase.passed ? "✓" : "✗"} Custom Test Result
                                  </span>
                                </div>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="chevron-down"
                                >
                                  <path d="m6 9 6 6 6-6"/>
                                </svg>
                              </div>

                              <div id={`custom-test-details-${index}`} className="px-4 py-3 font-mono text-sm border-t overflow-auto max-h-60 block">
                                <div className="grid grid-cols-1 gap-2">
                                  <div>
                                    <span className="text-gray-500 dark:text-gray-400">Input:</span>
                                    <div className="mt-1 bg-muted p-2 rounded overflow-auto">{testCase.input}</div>
                                  </div>
                                  {testCase.expected !== 'N/A' && (
                                    <div>
                                      <span className="text-gray-500 dark:text-gray-400">Expected Output:</span>
                                      <div className="mt-1 bg-muted p-2 rounded overflow-auto">{testCase.expected}</div>
                                    </div>
                                  )}
                                  <div>
                                    <span className="text-gray-500 dark:text-gray-400">Your Output:</span>
                                    <div className={`mt-1 p-2 rounded overflow-auto ${
                                      testCase.passed
                                        ? "bg-green-50 dark:bg-green-900/20"
                                        : "bg-red-50 dark:bg-red-900/20"
                                    }`}>
                                      {testCase.actual}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Custom test error */}
                    {customTestResult && !customTestResult.success && (
                      <div className="mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900/50 rounded-md p-4 overflow-auto max-h-[calc(100vh-300px)]">
                        <div className="flex items-center gap-2 mb-2">
                          <XCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                          <h3 className="font-medium">Error: {customTestResult.error?.type}</h3>
                        </div>

                        <p className="mt-1 text-red-600 dark:text-red-300">
                          {customTestResult.error?.type === 'VALIDATION_ERROR'
                            ? "Please check your input format. The input must be in the format: nums = [1,2,3], target = 9"
                            : customTestResult.error?.message}
                        </p>

                        {customTestResult.error?.details && (
                          <div className="font-mono text-sm mt-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-md overflow-auto max-h-60">
                            <pre className="whitespace-pre-wrap">{customTestResult.error.details}</pre>
                          </div>
                        )}

                        {customTestResult.error?.type === 'VALIDATION_ERROR' && (
                          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-900/50 rounded-md">
                            <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Example Valid Inputs:</h4>
                            <ul className="list-disc list-inside text-sm text-blue-600 dark:text-blue-400 space-y-1">
                              <li><code className="bg-muted px-1 py-0.5 rounded">nums = [1,2,3], target = 5</code></li>
                              <li><code className="bg-muted px-1 py-0.5 rounded">nums = [2,7,11,15], target = 9</code></li>
                              <li><code className="bg-muted px-1 py-0.5 rounded">nums = [3,3], target = 6</code></li>
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>

              {/* Fixed buttons area */}
              <div className="absolute bottom-0 right-0 left-0 flex justify-end space-x-2 bg-card p-4 border-t shadow-md z-10">
                <Button
                  variant="outline"
                  onClick={handleCompile}
                  disabled={isLoading}
                  className="h-10"
                >
                  {isLoading && compilationResult === null ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Compiling...
                    </>
                  ) : (
                    'Run'
                  )}
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="h-10 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
                >
                  {isLoading && compilationResult !== null ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    'Submit'
                  )}
                </Button>
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </CardContent>
    </Card>
  );
}