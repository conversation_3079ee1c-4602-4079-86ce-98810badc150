/**
 * Utility to validate Scala code for common errors
 * This is used when Judge<PERSON> fails to properly handle Scala code
 */

interface ScalaValidationResult {
  isValid: boolean;
  errors: string[];
}

export const scalaValidator = {
  /**
   * Validates Scala code for common syntax errors and typos
   * @param code The Scala code to validate
   * @returns Validation result with errors if any
   */
  validateScalaCode(code: string): ScalaValidationResult {
    const errors: string[] = [];
    
    // Check for common typos
    this.checkCommonTypos(code, errors);
    
    // Check for mismatched brackets
    this.checkMismatchedBrackets(code, errors);
    
    // Check for undefined methods
    this.checkUndefinedMethods(code, errors);
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },
  
  /**
   * Check for common typos in Scala code
   */
  checkCommonTypos(code: string, errors: string[]): void {
    const typos: Record<string, string> = {
      'indicplkes': 'indices',
      'indicoles': 'indices',
      'lenght': 'length',
      'containskey': 'contains',
      'mkstring': 'mkString',
      'zipwithindex': 'zipWithIndex',
      'foreachprint': 'foreach println',
      'printline': 'println'
    };
    
    const lines = code.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith('//')) continue;
      
      for (const [typo, correction] of Object.entries(typos)) {
        // Case-insensitive search for typos
        const lowerLine = line.toLowerCase();
        if (lowerLine.includes(typo.toLowerCase())) {
          // Find the actual typo with original casing
          const match = line.match(new RegExp(typo, 'i'));
          if (match) {
            errors.push(`Line ${i + 1}: Possible typo: '${match[0]}' should be '${correction}'.`);
          }
        }
      }
    }
  },
  
  /**
   * Check for mismatched brackets
   */
  checkMismatchedBrackets(code: string, errors: string[]): void {
    const stack: { char: string, line: number }[] = [];
    const lines = code.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith('//')) continue;
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        
        if (char === '{' || char === '[' || char === '(') {
          stack.push({ char, line: i + 1 });
        } else if (char === '}' || char === ']' || char === ')') {
          if (stack.length === 0) {
            errors.push(`Line ${i + 1}: Unexpected closing '${char}'.`);
            continue;
          }
          
          const last = stack.pop()!;
          const expected = this.getMatchingBracket(last.char);
          
          if (char !== expected) {
            errors.push(`Line ${i + 1}: Expected '${expected}' to match '${last.char}' from line ${last.line}, but found '${char}'.`);
          }
        }
      }
    }
    
    if (stack.length > 0) {
      for (const item of stack) {
        errors.push(`Line ${item.line}: Unclosed '${item.char}'.`);
      }
    }
  },
  
  /**
   * Check for undefined methods on common Scala types
   */
  checkUndefinedMethods(code: string, errors: string[]): void {
    const lines = code.split('\n');
    
    // Common Scala methods for different types
    const arrayMethods = ['length', 'indices', 'foreach', 'map', 'filter', 'mkString', 'contains', 'indexOf', 'sorted', 'reverse'];
    const mapMethods = ['get', 'contains', 'put', 'remove', 'keys', 'values', 'isEmpty', 'size', 'foreach', 'map', 'filter'];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith('//')) continue;
      
      // Check for Array method calls
      if (line.includes('Array') || line.includes('nums')) {
        // Look for method calls on arrays
        const methodCalls = line.match(/\.([\w]+)/g);
        if (methodCalls) {
          for (const methodCall of methodCalls) {
            const method = methodCall.substring(1); // Remove the dot
            
            // Check if it's a valid Array method
            if (!arrayMethods.includes(method) && 
                !this.isCommonScalaMethod(method) && 
                this.isLikelyMethodCall(line, method)) {
              
              // Check if it's a typo of a valid method
              const similarMethods = this.findSimilarMethods(method, arrayMethods);
              if (similarMethods.length > 0) {
                errors.push(`Line ${i + 1}: '${method}' is not a valid Array method. Did you mean '${similarMethods[0]}'?`);
              } else {
                errors.push(`Line ${i + 1}: '${method}' is not a valid Array method.`);
              }
            }
          }
        }
      }
      
      // Check for Map method calls
      if (line.includes('Map') || line.includes('map(')) {
        // Look for method calls on maps
        const methodCalls = line.match(/\.([\w]+)/g);
        if (methodCalls) {
          for (const methodCall of methodCalls) {
            const method = methodCall.substring(1); // Remove the dot
            
            // Check if it's a valid Map method
            if (!mapMethods.includes(method) && 
                !this.isCommonScalaMethod(method) && 
                this.isLikelyMethodCall(line, method)) {
              
              // Check if it's a typo of a valid method
              const similarMethods = this.findSimilarMethods(method, mapMethods);
              if (similarMethods.length > 0) {
                errors.push(`Line ${i + 1}: '${method}' is not a valid Map method. Did you mean '${similarMethods[0]}'?`);
              } else {
                errors.push(`Line ${i + 1}: '${method}' is not a valid Map method.`);
              }
            }
          }
        }
      }
    }
  },
  
  /**
   * Get the matching closing bracket for an opening bracket
   */
  getMatchingBracket(char: string): string {
    switch (char) {
      case '{': return '}';
      case '[': return ']';
      case '(': return ')';
      default: return '';
    }
  },
  
  /**
   * Check if a method is a common Scala method
   */
  isCommonScalaMethod(method: string): boolean {
    const commonMethods = [
      'toString', 'hashCode', 'equals', 'getClass', 'clone', 'finalize', 'notify', 'notifyAll', 'wait',
      'apply', 'unapply', 'head', 'tail', 'init', 'last', 'take', 'drop', 'takeWhile', 'dropWhile',
      'find', 'exists', 'forall', 'count', 'foldLeft', 'foldRight', 'reduceLeft', 'reduceRight',
      'sum', 'product', 'min', 'max', 'flatten', 'flatMap', 'collect', 'zip', 'unzip', 'zipWithIndex',
      'partition', 'span', 'groupBy', 'sliding', 'updated', 'patch', 'padTo', 'distinct', 'sortBy',
      'sortWith', 'transpose', 'slice', 'splitAt', 'indexWhere', 'lastIndexWhere', 'indexOfSlice',
      'lastIndexOfSlice', 'containsSlice', 'corresponds', 'intersect', 'diff', 'union', 'toArray',
      'toList', 'toSeq', 'toSet', 'toMap', 'toVector', 'toBuffer', 'toStream', 'toIterator'
    ];
    
    return commonMethods.includes(method);
  },
  
  /**
   * Check if a string is likely a method call based on context
   */
  isLikelyMethodCall(line: string, method: string): boolean {
    // Check if it's used in a context that suggests it's a method call
    return line.includes(`.${method}`) && // Has a dot before it
           !line.includes(`"${method}"`) && // Not a string literal
           !line.includes(`'${method}'`); // Not a character literal
  },
  
  /**
   * Find similar methods to a potentially misspelled one
   */
  findSimilarMethods(method: string, validMethods: string[]): string[] {
    return validMethods.filter(validMethod => {
      // Simple similarity check - at least 60% of characters match
      let matches = 0;
      const minLength = Math.min(method.length, validMethod.length);
      const maxLength = Math.max(method.length, validMethod.length);
      
      for (let i = 0; i < minLength; i++) {
        if (method[i].toLowerCase() === validMethod[i].toLowerCase()) matches++;
      }
      
      return matches / maxLength > 0.6;
    });
  }
};
