import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useStytch, useStytchUser } from '@stytch/react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Card } from '../../components/ui/card';
import { useAuth } from '@/context/AuthContext';
import axios from 'axios';

type OnboardingStep = {
  id: string;
  title: string;
  description: string;
  isSkippable: boolean;
  component: React.ReactNode;
};

type UserData = {
  fullName: string;
  jobTitle: string;
  company: string;
  programmingLanguages: string[];
  experience: string;
  preferredTheme: 'light' | 'dark' | 'system';
  notifications: boolean;
};

// API base URL
const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export default function Onboarding() {
  const navigate = useNavigate();
  const stytch = useStytch();
  const { user, completeOnboarding, isLoading: authLoading } = useAuth();
  const { user: stytchUser } = useStytchUser();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [userData, setUserData] = useState<UserData>({
    fullName: '',
    jobTitle: '',
    company: '',
    programmingLanguages: [],
    experience: 'intermediate',
    preferredTheme: 'system',
    notifications: true,
  });

  // Debug logs
  useEffect(() => {
    console.log('Onboarding component mounted');
    console.log('Auth user:', user);
    console.log('Stytch user:', stytchUser);
    console.log('Auth loading:', authLoading);
  }, [user, stytchUser, authLoading]);

  // Prefill form with user data if available
  useEffect(() => {
    if (user && stytchUser) {
      console.log('User data available for onboarding:', {
        userId: user.id,
        email: user.email,
        role: user.role,
        metadata: user.metadata,
        stytchUserId: stytchUser.user_id
      });
      setInitialLoadComplete(true);

      // Check if user has already completed onboarding
      let onboardingComplete = false;

      // Try to parse metadata from user object
      try {
        if (user.metadata) {
          const metadata = typeof user.metadata === 'string'
            ? JSON.parse(user.metadata)
            : user.metadata;

          console.log('Parsed user metadata:', metadata);
          onboardingComplete = metadata.onboardingComplete === true;
        }
      } catch (error) {
        console.error('Error parsing user metadata:', error);
      }

      // Also check Stytch metadata
      const stytchMetadata = stytchUser.untrusted_metadata || {};
      console.log('Stytch metadata:', stytchMetadata);

      if (stytchMetadata.onboarding_complete === true) {
        console.log('Stytch metadata indicates onboarding is complete');
        onboardingComplete = true;
      }

      // Also check direct onboardingComplete property on user
      if (user.onboardingComplete === true) {
        console.log('User object directly indicates onboarding is complete');
        onboardingComplete = true;
      }

      console.log('Final onboarding status determination:', onboardingComplete);

      if (onboardingComplete) {
        console.log('Onboarding already complete, redirecting to dashboard');
        // Redirect based on user role
        const userRole = user.role || 'student'; // Default to student if no role

        if (userRole === 'admin') {
          console.log('Admin user detected, redirecting to admin dashboard');
          navigate('/admin');
        } else {
          console.log('Student user detected, redirecting to dashboard');
          navigate('/dashboard');
        }
        return;
      } else {
        console.log('Onboarding not complete, continuing with onboarding process');
      }

      // Prefill user data
      setUserData(prevData => {
        const updatedData = {
          ...prevData,
          fullName: `${user.first_name || ''} ${user.last_name || ''}`.trim() || prevData.fullName,
          jobTitle: (stytchMetadata.job_title as string) || prevData.jobTitle,
          company: (stytchMetadata.company as string) || prevData.company,
          programmingLanguages:
            (stytchMetadata.programming_languages as string[]) || prevData.programmingLanguages,
          experience: (stytchMetadata.experience as string) || prevData.experience,
          preferredTheme:
            (stytchMetadata.preferred_theme as 'light' | 'dark' | 'system') || prevData.preferredTheme,
          notifications: (stytchMetadata.notifications as boolean) ?? prevData.notifications,
        };

        console.log('Prefilled user data:', updatedData);
        return updatedData;
      });
    } else {
      console.log('User or Stytch user data not available yet');
    }
  }, [user, stytchUser, navigate]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  // Handle programming language selection
  const handleLanguageToggle = (language: string) => {
    setUserData(prev => {
      const languages = [...prev.programmingLanguages];
      if (languages.includes(language)) {
        return {
          ...prev,
          programmingLanguages: languages.filter(lang => lang !== language),
        };
      } else {
        return {
          ...prev,
          programmingLanguages: [...languages, language],
        };
      }
    });
  };

  // Handle experience level selection
  const handleExperienceChange = (level: string) => {
    setUserData(prev => ({
      ...prev,
      experience: level,
    }));
  };

  // Handle theme preference selection
  const handleThemeChange = (theme: 'light' | 'dark' | 'system') => {
    setUserData(prev => ({
      ...prev,
      preferredTheme: theme,
    }));
  };

  // Save user data to Stytch's untrusted metadata and update backend
  const saveUserData = async () => {
    console.log('Saving user data to Stytch and backend:', userData);

    try {
      if (!user || !stytchUser) {
        throw new Error('User data not available');
      }

      // 1. Update the user's data in Stytch
      console.log('Updating Stytch user data...');
      try {
        await stytch.user.update({
          name: {
            first_name: userData.fullName.split(' ')[0] || '',
            last_name: userData.fullName.split(' ').slice(1).join(' ') || '',
          },
          untrusted_metadata: {
            // Store all user preferences in untrusted_metadata
            job_title: userData.jobTitle,
            company: userData.company,
            programming_languages: userData.programmingLanguages,
            experience: userData.experience,
            preferred_theme: userData.preferredTheme,
            notifications: userData.notifications,
            onboarding_complete: true,
          },
        });
        console.log('Stytch user data updated successfully');
      } catch (stytchError) {
        console.error('Error updating Stytch user data:', stytchError);
        // Continue with backend updates even if Stytch update fails
      }

      // 2. Update backend user data
      console.log('Updating backend user data...');

      // First try the completeOnboarding function from AuthContext
      try {
        await completeOnboarding();
        console.log('Onboarding completed via AuthContext');
      } catch (onboardingError) {
        console.error('Error in completeOnboarding:', onboardingError);
      }

      // Also update user metadata directly in the backend as a fallback
      if (user.id) {
        try {
          const metadataToUpdate = {
            onboardingComplete: true,
            job_title: userData.jobTitle,
            company: userData.company,
            programming_languages: userData.programmingLanguages,
            experience: userData.experience,
            preferred_theme: userData.preferredTheme,
            notifications: userData.notifications
          };

          console.log('Updating user metadata directly:', metadataToUpdate);

          const response = await axios.put(`${API_URL}/auth/user/${user.id}`, {
            metadata: JSON.stringify(metadataToUpdate)
          });

          console.log('Backend user update response:', response.data);
        } catch (directUpdateError) {
          console.error('Error updating user metadata directly:', directUpdateError);
        }
      }

      console.log('User data save process completed');
      return Promise.resolve();
    } catch (error) {
      console.error('Error in saveUserData:', error);
      return Promise.reject(error);
    }
  };

  // Define all onboarding steps
  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to CodeTest Platform',
      description: "Let's get you set up with your personalized coding experience",
      isSkippable: false,
      component: (
        <div className="flex flex-col items-center justify-center space-y-6 text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl font-bold">Welcome to CodeTest Platform</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              The ultimate platform to test and improve your coding skills
            </p>
          </motion.div>
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <div className="w-32 h-32 bg-blue-500 rounded-xl flex items-center justify-center text-white font-bold text-2xl">
              CodeTest
            </div>
          </motion.div>
        </div>
      ),
    },
    {
      id: 'personal-info',
      title: 'Personal Information',
      description: 'Tell us a bit about yourself',
      isSkippable: false,
      component: (
        <div className="space-y-6">
          <div className="space-y-2 m-1">
            <Label htmlFor="fullName" className="text-base font-medium">
              Full Name
            </Label>
            <Input
              id="fullName"
              name="fullName"
              value={userData.fullName}
              onChange={handleInputChange}
              placeholder="John Doe"
              className="h-11 px-4 transition-all border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              required
            />
          </div>
          <div className="space-y-2 m-1">
            <Label htmlFor="jobTitle" className="text-base font-medium">
              Job Title
            </Label>
            <Input
              id="jobTitle"
              name="jobTitle"
              value={userData.jobTitle}
              onChange={handleInputChange}
              placeholder="Software Engineer"
              className="h-11 px-4 transition-all border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            />
          </div>
          <div className="space-y-2 m-1">
            <Label htmlFor="company" className="text-base font-medium">
              Company/Organization
            </Label>
            <Input
              id="company"
              name="company"
              value={userData.company}
              onChange={handleInputChange}
              placeholder="Acme Inc."
              className="h-11 px-4 transition-all border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            />
          </div>
        </div>
      ),
    },
    {
      id: 'programming-languages',
      title: 'Programming Languages',
      description: "Select the programming languages you're interested in",
      isSkippable: true,
      component: (
        <div className="space-y-6 m-1">
          <div className="space-y-2">
            <Label className="text-base font-medium">Programming Languages</Label>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Choose the programming languages you want to practice with
            </p>
          </div>
          <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
            {['JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'Go', 'Rust', 'Ruby', 'PHP'].map(
              language => (
                <Button
                  key={language}
                  type="button"
                  variant={userData.programmingLanguages.includes(language) ? 'default' : 'outline'}
                  onClick={() => handleLanguageToggle(language)}
                  className={`justify-start h-11 text-base transition-all ${
                    userData.programmingLanguages.includes(language)
                      ? 'bg-blue-500 hover:bg-blue-600 text-white'
                      : 'hover:border-blue-400 hover:text-blue-500'
                  }`}
                >
                  {language}
                </Button>
              )
            )}
          </div>
        </div>
      ),
    },
    {
      id: 'experience-level',
      title: 'Experience Level',
      description: 'Tell us about your coding experience',
      isSkippable: true,
      component: (
        <div className="space-y-6 m-1">
          <div className="space-y-2">
            <Label className="text-base font-medium">Experience Level</Label>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              This helps us tailor the difficulty of challenges to your skill level
            </p>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {[
              {
                id: 'beginner',
                label: 'Beginner',
                description: 'New to coding or learning the basics',
                icon: '🌱',
              },
              {
                id: 'intermediate',
                label: 'Intermediate',
                description: 'Comfortable with coding but still learning',
                icon: '📚',
              },
              {
                id: 'advanced',
                label: 'Advanced',
                description: 'Experienced developer with strong skills',
                icon: '💻',
              },
              {
                id: 'expert',
                label: 'Expert',
                description: 'Professional with deep knowledge and experience',
                icon: '🏆',
              },
            ].map(level => (
              <Card
                key={level.id}
                className={`p-5 cursor-pointer transition-all hover:shadow-md ${
                  userData.experience === level.id
                    ? 'border-2 border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'hover:border-blue-200'
                }`}
                onClick={() => handleExperienceChange(level.id)}
              >
                <div className="flex items-center gap-4">
                  <div className="text-2xl">{level.icon}</div>
                  <div>
                    <h3 className="font-medium text-base">{level.label}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{level.description}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      ),
    },
    {
      id: 'preferences',
      title: 'Preferences',
      description: 'Customize your experience',
      isSkippable: true,
      component: (
        <div className="space-y-8 m-1">
          <div className="space-y-3">
            <Label className="text-base font-medium">Theme Preference</Label>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Choose your preferred color theme for the platform
            </p>
            <div className="grid grid-cols-3 gap-3">
              {[
                { id: 'light', label: 'Light', icon: '☀️' },
                { id: 'dark', label: 'Dark', icon: '🌙' },
                { id: 'system', label: 'System', icon: '🖥️' },
              ].map(theme => (
                <Button
                  key={theme.id}
                  type="button"
                  variant={userData.preferredTheme === theme.id ? 'default' : 'outline'}
                  onClick={() => handleThemeChange(theme.id as 'light' | 'dark' | 'system')}
                  className={`h-14 transition-all ${
                    userData.preferredTheme === theme.id
                      ? 'bg-blue-500 hover:bg-blue-600 text-white'
                      : 'hover:border-blue-400 hover:text-blue-500'
                  }`}
                >
                  <div className="flex flex-col items-center">
                    <span className="text-lg mb-1">{theme.icon}</span>
                    <span>{theme.label}</span>
                  </div>
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-3">
            <Label className="text-base font-medium">Notifications</Label>
            <Card className="p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Challenge Updates</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Receive notifications about new challenges and updates
                  </p>
                </div>
                <div className="flex items-center h-6">
                  <input
                    type="checkbox"
                    id="notifications"
                    name="notifications"
                    checked={userData.notifications}
                    onChange={handleInputChange}
                    className="h-5 w-5 rounded border-gray-300 text-blue-500 focus:ring-blue-500"
                    aria-label="Enable notifications"
                  />
                </div>
              </div>
            </Card>
          </div>
        </div>
      ),
    },
    {
      id: 'complete',
      title: 'All Set!',
      description: 'Your personalized experience is ready',
      isSkippable: false,
      component: (
        <div className="flex flex-col items-center justify-center space-y-6 text-center py-4">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-green-500 dark:text-green-400 bg-green-50 dark:bg-green-900/20 p-6 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="72"
              height="72"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
              <polyline points="22 4 12 14.01 9 11.01" />
            </svg>
          </motion.div>
          <div className="space-y-3">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">You're all set!</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Your profile has been created and your preferences have been saved.
            </p>
            <p className="text-gray-600 dark:text-gray-300">
              You're ready to start coding challenges and improve your skills!
            </p>
          </div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className="mt-4"
          >
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 p-4 max-w-md">
              <div className="flex items-start">
                <div className="flex-shrink-0 text-blue-500 mr-3 mt-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="16" x2="12" y2="12"></line>
                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Getting Started</h3>
                  <p className="text-sm text-blue-700 dark:text-blue-400">
                    Check out the dashboard to find your first coding challenge!
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      ),
    },
  ];

  const currentStep = steps[currentStepIndex];

  const goToNextStep = async () => {
    if (currentStepIndex === steps.length - 1) {
      // This is the last step, save data and redirect
      setIsLoading(true);
      try {
        console.log('Final step reached, saving user data...');
        await saveUserData();
        console.log('User data saved successfully, preparing for redirect');

        // Add a small delay to show the loading animation and ensure state updates
        setTimeout(() => {
          try {
            // Redirect based on user role
            const userRole = user?.role || 'student'; // Default to student if no role

            console.log('Redirecting user with role:', userRole);

            if (userRole === 'admin') {
              console.log('Admin user detected, redirecting to admin dashboard after onboarding');
              navigate('/admin');
            } else {
              console.log('Student user detected, redirecting to dashboard after onboarding');
              navigate('/dashboard');
            }
          } catch (redirectError) {
            console.error('Error during redirect:', redirectError);
            // Fallback to dashboard if there's an error
            navigate('/dashboard');
          }
        }, 2000); // Increased timeout to ensure all state updates are processed
      } catch (error) {
        console.error('Error during onboarding completion:', error);
        setIsLoading(false);
        // Show an error message to the user
        alert('There was an error completing your onboarding. Please try again or contact support.');
      }
      return;
    }

    setDirection(1);
    setCurrentStepIndex(prev => prev + 1);
  };

  const goToPreviousStep = () => {
    if (currentStepIndex > 0) {
      setDirection(-1);
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const skipStep = () => {
    if (currentStep.isSkippable && currentStepIndex < steps.length - 1) {
      setDirection(1);
      setCurrentStepIndex(prev => prev + 1);
    }
  };

  // Animation variants
  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
    }),
  };

  // Progress calculation
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  // Show loading state if auth is still loading or user/stytchUser is not available yet
  if (authLoading || !user || !stytchUser || !initialLoadComplete) {
    console.log('Onboarding loading state:', {
      authLoading,
      user: user ? {
        id: user.id,
        email: user.email,
        role: user.role,
        metadata: user.metadata
      } : null,
      stytchUser: stytchUser ? {
        user_id: stytchUser.user_id,
        email: stytchUser.emails?.[0]?.email
      } : null
    });

    return (
      <div className="min-h-screen flex items-center justify-center bg-[#f5f7fa] dark:bg-gray-900">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-8 flex flex-col items-center justify-center max-w-md mx-4 shadow-xl"
        >
          <motion.div
            className="w-16 h-16 mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
          >
            <svg
              className="w-full h-full text-blue-500"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeDasharray="60 60"
                strokeDashoffset="60"
                strokeLinejoin="round"
              />
              <path
                d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeDasharray="60 60"
                strokeDashoffset="0"
                strokeLinejoin="round"
                className="text-blue-200 dark:text-blue-800"
              />
            </svg>
          </motion.div>
          <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">
            Loading Onboarding
          </h3>
          <p className="text-gray-600 dark:text-gray-300 text-center mb-4">
            Please wait while we prepare your onboarding experience...
          </p>
          <motion.div className="h-1 bg-gray-200 dark:bg-gray-700 w-full rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-blue-500"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 1.5, ease: 'easeInOut', repeat: Infinity }}
            />
          </motion.div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-[#f5f7fa] dark:bg-gray-900 relative">
      {/* Header with logo and progress */}
      <header className="bg-white dark:bg-gray-800 shadow-md py-4 px-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <img src="/logo.svg" alt="CodeTest Platform" className="h-8 w-auto mr-3" />
            <span className="text-xl font-bold text-gray-900 dark:text-white">CodeTest</span>
          </div>

          {/* Progress indicator */}
          <div className="hidden md:flex items-center space-x-2">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`flex items-center justify-center h-8 w-8 rounded-full text-sm font-medium
                    ${
                      index < currentStepIndex
                        ? 'bg-green-500 text-white'
                        : index === currentStepIndex
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                    }
                  `}
                >
                  {index < currentStepIndex ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  ) : (
                    index + 1
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className="w-10 h-1 bg-gray-200 dark:bg-gray-700 mx-1 relative">
                    <div
                      className={`h-full bg-blue-500 absolute top-0 left-0 transition-all duration-300 ${
                        index < currentStepIndex ? 'w-full' : 'w-0'
                      }`}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Mobile progress bar */}
          <div className="w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full md:hidden">
            <motion.div
              className="h-full bg-blue-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-grow flex flex-col">
        <div className="flex-grow flex items-center justify-center p-4 md:p-8">
          <div className="w-full max-w-4xl bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
            <div className="md:flex">
              {/* Left sidebar with step info - only visible on md and larger */}
              <div className="hidden md:block w-1/3 bg-[#172B4D] text-white p-8">
                <h2 className="text-2xl font-bold mb-6">Step {currentStepIndex + 1}</h2>
                <div className="space-y-6">
                  {steps.map((step, index) => (
                    <div
                      key={step.id}
                      className={`flex items-start space-x-3 ${
                        index === currentStepIndex
                          ? 'opacity-100'
                          : index < currentStepIndex
                            ? 'opacity-70'
                            : 'opacity-40'
                      }`}
                    >
                      <div
                        className={`flex-shrink-0 mt-1 flex items-center justify-center h-6 w-6 rounded-full text-xs font-medium
                          ${
                            index < currentStepIndex
                              ? 'bg-green-500 text-white'
                              : index === currentStepIndex
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-600 text-gray-300'
                          }
                        `}
                      >
                        {index < currentStepIndex ? (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        ) : (
                          index + 1
                        )}
                      </div>
                      <div>
                        <h3
                          className={`font-medium ${index === currentStepIndex ? 'text-blue-300' : 'text-white'}`}
                        >
                          {step.title}
                        </h3>
                        <p className="text-sm text-gray-300 mt-1">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Main content area */}
              <div className="md:w-2/3 p-6 md:p-8">
                {/* Mobile step header - only visible on small screens */}
                <div className="md:hidden mb-6">
                  <h2 className="text-xl font-bold">{currentStep.title}</h2>
                  <p className="text-gray-600 dark:text-gray-300">{currentStep.description}</p>
                </div>

                {/* Step content with animation */}
                <div className="min-h-[350px] flex items-center justify-center overflow-hidden">
                  <AnimatePresence mode="wait" custom={direction}>
                    <motion.div
                      key={currentStep.id}
                      custom={direction}
                      variants={variants}
                      initial="enter"
                      animate="center"
                      exit="exit"
                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                      className="w-full"
                    >
                      {currentStep.component}
                    </motion.div>
                  </AnimatePresence>
                </div>

                {/* Navigation buttons */}
                <div className="flex justify-between mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="outline"
                    onClick={goToPreviousStep}
                    disabled={currentStepIndex === 0}
                    className="px-6"
                  >
                    Back
                  </Button>
                  <div className="flex space-x-3">
                    {currentStep.isSkippable && (
                      <Button variant="ghost" onClick={skipStep} className="px-6">
                        Skip
                      </Button>
                    )}
                    <Button onClick={goToNextStep} className="px-8 bg-[#0052CC] hover:bg-[#0747A6]">
                      {currentStepIndex === steps.length - 1 ? 'Finish' : 'Next'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 shadow-inner py-4 px-6 text-center text-sm text-gray-600 dark:text-gray-400">
        <p>© {new Date().getFullYear()} CodeTest Platform. All rights reserved.</p>
      </footer>

      {/* Loading overlay */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50"
          >
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-lg p-8 flex flex-col items-center justify-center max-w-md mx-4"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: 'spring', damping: 20 }}
            >
              <motion.div
                className="w-16 h-16 mb-4"
                animate={{
                  rotate: 360,
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: 'linear',
                }}
              >
                <svg
                  className="w-full h-full text-blue-500"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeDasharray="60 60"
                    strokeDashoffset="60"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeDasharray="60 60"
                    strokeDashoffset="0"
                    strokeLinejoin="round"
                    className="text-blue-200 dark:text-blue-800"
                  />
                </svg>
              </motion.div>
              <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">
                Setting up your dashboard
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-center mb-4">
                Please wait while we prepare your personalized coding experience...
              </p>
              <motion.div className="h-1 bg-gray-200 dark:bg-gray-700 w-full rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-blue-500"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 1.5, ease: 'easeInOut' }}
                />
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
