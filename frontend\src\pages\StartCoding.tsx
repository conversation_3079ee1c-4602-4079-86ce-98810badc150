import { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Question } from '@/components/Question';
import { Editor } from '@/components/Editor';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { IconArrowLeft, IconAlertTriangle } from '@tabler/icons-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface CompilationResult {
  success: boolean;
  results?: {
    passed: number;
    total: number;
    testCases: {
      passed: boolean;
      input: string;
      expected: string;
      actual: string;
    }[];
    executionTime?: string;
    memoryUsed?: number;
    failedTestCase?: {
      index: number;
      input: string;
      expected: string;
      actual: string;
    };
  };
  error?: {
    type: string;
    message: string;
    details?: string;
  };
  message?: string;
}

// Sample question data (this would typically come from an API)
const sampleQuestion = {
  title: "Two Sum",
  description: `Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.
You may assume that each input would have exactly one solution, and you may not use the same element twice.
You can return the answer in any order.`,
  examples: [
    {
      input: "nums = [2,7,11,15], target = 9",
      output: "[0,1]",
      explanation: "Because nums[0] + nums[1] == 9, we return [0, 1]."
    },
    {
      input: "nums = [3,2,4], target = 6",
      output: "[1,2]",
      explanation: "Because nums[1] + nums[2] == 6, we return [1, 2]."
    }
  ],
  constraints: [
    "2 <= nums.length <= 104",
    "-109 <= nums[i] <= 109",
    "-109 <= target <= 109",
    "Only one valid answer exists."
  ]
};

export default function StartCoding() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showFullscreenModal, setShowFullscreenModal] = useState(true);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [warningVisible, setWarningVisible] = useState(false);
  const [warningCount, setWarningCount] = useState(0);
  const lastActiveTime = useRef(Date.now());
  const fullscreenButtonRef = useRef<HTMLButtonElement>(null);
  const documentVisibilityRef = useRef(true);

  const toggleFullScreen = useCallback(() => {
    try {
      if (!document.fullscreenElement) {
        // Different browsers might have different implementations
        const docElement = document.documentElement;
        const requestMethod = docElement.requestFullscreen || 
                             (docElement as any).webkitRequestFullscreen || 
                             (docElement as any).mozRequestFullScreen || 
                             (docElement as any).msRequestFullscreen;
        
        if (requestMethod) {
          requestMethod.call(docElement).then(() => {
            setIsFullScreen(true);
          }).catch(err => {
            console.log('Error attempting to enable full-screen mode:', err.message);
          });
        } else {
          console.log('Fullscreen API is not supported in this browser');
        }
      } else {
        const exitMethod = document.exitFullscreen || 
                          (document as any).webkitExitFullscreen || 
                          (document as any).mozCancelFullScreen || 
                          (document as any).msExitFullscreen;
        
        if (exitMethod) {
          exitMethod.call(document).then(() => {
            setIsFullScreen(false);
          }).catch(err => {
            console.log('Error attempting to exit full-screen mode:', err.message);
          });
        }
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  }, []);

  // Handle the start button click in the modal
  const handleStartCoding = () => {
    if (!termsAccepted) {
      addWarning("You must accept the proctoring terms before starting");
      return;
    }
    
    setShowFullscreenModal(false);
    // This will be called directly after a user interaction (button click)
    // which is allowed by browsers
    toggleFullScreen();
  };

  // Function to add warning and show warning alert
  const addWarning = useCallback((message: string) => {
    const now = new Date();
    const timestamp = now.toLocaleTimeString();
    const warningMessage = `[${timestamp}] ${message}`;
    
    setWarnings(prev => [warningMessage, ...prev]);
    setWarningVisible(true);
    setWarningCount(prev => prev + 1);
    
    // Hide warning after 5 seconds
    setTimeout(() => {
      setWarningVisible(false);
    }, 5000);
  }, []);

  // Prevent tab visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isDocumentHidden = document.hidden;
      
      if (documentVisibilityRef.current && isDocumentHidden) {
        addWarning("Tab switching or minimizing detected. This activity is being recorded.");
      }
      
      documentVisibilityRef.current = !isDocumentHidden;
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [addWarning]);

  // Prevent copy paste and other keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Allow F11 for fullscreen toggle
      if (e.key === 'F11') {
        e.preventDefault();
        toggleFullScreen();
        return;
      }
      
      // Prevent Ctrl+C (copy)
      if (e.ctrlKey && e.key === 'c') {
        e.preventDefault();
        addWarning("Copy operation is not allowed in proctored environment");
        return;
      }
      
      // Prevent Ctrl+V (paste)
      if (e.ctrlKey && e.key === 'v') {
        e.preventDefault();
        addWarning("Paste operation is not allowed in proctored environment");
        return;
      }
      
      // Prevent Ctrl+X (cut)
      if (e.ctrlKey && e.key === 'x') {
        e.preventDefault();
        addWarning("Cut operation is not allowed in proctored environment");
        return;
      }
      
      // Prevent Ctrl+F (find)
      if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        addWarning("Search operation is not allowed in proctored environment");
        return;
      }
      
      // Prevent Ctrl+H (history/search & replace)
      if (e.ctrlKey && e.key === 'h') {
        e.preventDefault();
        addWarning("Search & replace is not allowed in proctored environment");
        return;
      }
      
      // Prevent Alt+Tab (though this may be handled by the OS)
      if (e.altKey && e.key === 'Tab') {
        e.preventDefault();
        addWarning("Tab switching is not allowed in proctored environment");
        return;
      }
    };

    // Prevent right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      addWarning("Right-click menu is not allowed in proctored environment");
      return false;
    };

    // Prevent copy/paste events
    const handleCopy = (e: ClipboardEvent) => {
      e.preventDefault();
      addWarning("Copy operation is not allowed in proctored environment");
      return false;
    };

    const handlePaste = (e: ClipboardEvent) => {
      e.preventDefault();
      addWarning("Paste operation is not allowed in proctored environment");
      return false;
    };

    // Prevent drag and drop
    const handleDrag = (e: DragEvent) => {
      e.preventDefault();
      addWarning("Drag and drop is not allowed in proctored environment");
      return false;
    };

    // Add all event listeners
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('contextmenu', handleContextMenu);
    window.addEventListener('copy', handleCopy);
    window.addEventListener('paste', handlePaste);
    window.addEventListener('dragstart', handleDrag);
    window.addEventListener('drop', handleDrag);

    // Detect user focus/activity
    const checkUserActivity = () => {
      const currentTime = Date.now();
      if (currentTime - lastActiveTime.current > 60000) { // 1 minute of inactivity
        addWarning("Extended inactivity detected. Please stay engaged with the task.");
      }
      lastActiveTime.current = currentTime;
    };

    // Monitor user activity
    const handleUserActivity = () => {
      lastActiveTime.current = Date.now();
    };

    window.addEventListener('mousemove', handleUserActivity);
    window.addEventListener('keydown', handleUserActivity);
    window.addEventListener('mousedown', handleUserActivity);
    window.addEventListener('touchstart', handleUserActivity);
    
    // Check activity every minute
    const activityInterval = setInterval(checkUserActivity, 60000);

    // Auth check and cleanup
    if (!user) {
      navigate('/login');
    } else {
      setIsLoading(false);
    }

    // Update state when fullscreen changes from browser controls
    const handleFullscreenChange = () => {
      const isInFullScreen = !!document.fullscreenElement;
      setIsFullScreen(isInFullScreen);
      
      // If user exits fullscreen without using our controls, warn them
      if (!isInFullScreen && !showFullscreenModal) {
        addWarning("Exiting fullscreen mode is not recommended during a proctored session");
      }
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // Clean up all event listeners when component unmounts
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('contextmenu', handleContextMenu);
      window.removeEventListener('copy', handleCopy);
      window.removeEventListener('paste', handlePaste);
      window.removeEventListener('dragstart', handleDrag);
      window.removeEventListener('drop', handleDrag);
      window.removeEventListener('mousemove', handleUserActivity);
      window.removeEventListener('keydown', handleUserActivity);
      window.removeEventListener('mousedown', handleUserActivity);
      window.removeEventListener('touchstart', handleUserActivity);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('visibilitychange', () => {});
      clearInterval(activityInterval);
      
      if (document.fullscreenElement) {
        const exitMethod = document.exitFullscreen || 
                          (document as any).webkitExitFullscreen || 
                          (document as any).mozCancelFullScreen || 
                          (document as any).msExitFullscreen;
        
        if (exitMethod) {
          exitMethod.call(document).catch(err => {
            console.log('Error attempting to exit full-screen mode:', err.message);
          });
        }
      }
    };
  }, [user, navigate, toggleFullScreen, addWarning, showFullscreenModal]);

  const handleCompile = async (code: string, language: string): Promise<CompilationResult> => {
    try {
      const response = await fetch('http://localhost:3001/api/coding/compile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, language }),
      });

      if (!response.ok) {
        throw new Error('Failed to compile code');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return {
        success: false,
        error: {
          type: 'API_ERROR',
          message: 'Failed to compile code',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  };

  const handleSubmit = async (code: string, language: string): Promise<CompilationResult> => {
    try {
      const response = await fetch('http://localhost:3001/api/coding/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, language }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit code');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return {
        success: false,
        error: {
          type: 'API_ERROR',
          message: 'Failed to submit code',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      {/* Fullscreen request modal */}
      {showFullscreenModal && (
        <div className="fixed inset-0 bg-background bg-opacity-90 flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg shadow-lg max-w-lg w-full text-center border border-border">
            <h2 className="text-xl font-bold mb-4">Proctored Coding Environment</h2>
            
            <div className="mb-6 text-left text-muted-foreground space-y-4">
              <p className="font-semibold text-foreground">This is a secure, proctored coding environment with the following restrictions:</p>
              
              <ul className="list-disc pl-5 space-y-2">
                <li>Your session will be conducted in fullscreen mode</li>
                <li>Copy and paste operations are disabled</li>
                <li>Browser search functions (Ctrl+F, Ctrl+H) are disabled</li>
                <li>Tab switching and window minimization are monitored</li>
                <li>Attempts to navigate away from this page are recorded</li>
                <li>All keyboard and mouse activities are monitored</li>
                <li>Extended periods of inactivity will be flagged</li>
              </ul>
              
              <Alert variant="destructive" className="mt-4">
                <IconAlertTriangle className="h-4 w-4" />
                <AlertTitle>Important</AlertTitle>
                <AlertDescription>
                  Any violation of these restrictions will be recorded as a potential academic integrity breach. Multiple violations may result in automatic test termination.
                </AlertDescription>
              </Alert>
              
              <div className="flex items-start space-x-2 mt-4">
                <input
                  type="checkbox"
                  id="terms"
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                  className="mt-1 h-4 w-4 rounded border-border text-primary focus:ring-primary"
                />
                <label
                  htmlFor="terms"
                  className="text-sm leading-tight cursor-pointer"
                >
                  I understand that my activities are being monitored and I agree to follow the proctoring rules.
                </label>
              </div>
            </div>
            
            <div className="flex justify-center gap-4">
              <Button 
                onClick={handleStartCoding} 
                size="lg" 
                className="px-8"
                disabled={!termsAccepted}
              >
                Start Proctored Session
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Warning notification */}
      {warningVisible && (
        <div className="fixed top-4 right-4 z-50 max-w-md w-full transition-opacity duration-500 opacity-100">
          <Alert variant="destructive">
            <IconAlertTriangle className="h-4 w-4" />
            <AlertTitle>Warning {warningCount > 1 ? `(${warningCount})` : ''}</AlertTitle>
            <AlertDescription>
              {warnings[0]}
            </AlertDescription>
          </Alert>
        </div>
      )}

      <div className="flex flex-col h-screen w-screen overflow-hidden bg-background">
        {/* Header with proctor badge */}
        <header className="flex h-14 shrink-0 items-center border-b bg-background px-4 lg:px-6 z-10">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              addWarning("Attempting to leave the coding environment will be recorded");
              
              // Show confirmation before allowing exit
              const confirmExit = window.confirm("Are you sure you want to leave this proctored session? This action will be recorded.");
              
              if (confirmExit) {
                // Exit full screen when navigating away
                if (document.fullscreenElement) {
                  const exitMethod = document.exitFullscreen || 
                                    (document as any).webkitExitFullscreen || 
                                    (document as any).mozCancelFullScreen || 
                                    (document as any).msExitFullscreen;
                  
                  if (exitMethod) {
                    exitMethod.call(document).catch(err => {
                      console.log('Error attempting to exit full-screen mode:', err.message);
                    });
                  }
                }
                navigate('/dashboard');
              }
            }}
            className="mr-2"
          >
            <IconArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back to Dashboard</span>
          </Button>
          
          <h1 className="text-lg font-semibold">Two Sum</h1>
          
          {/* Proctor status indicator */}
          <div className="flex items-center ml-auto mr-4 gap-2">
            <span className="relative flex h-3 w-3">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-destructive opacity-75"></span>
              <span className="relative inline-flex rounded-full h-3 w-3 bg-destructive"></span>
            </span>
            <span className="text-xs font-medium">Proctored Session Active</span>
          </div>
          
          {/* Toggle fullscreen button */}
          <Button 
            ref={fullscreenButtonRef}
            variant="ghost" 
            size="sm"
            className="text-xs"
            onClick={toggleFullScreen}
            title={isFullScreen ? "Exit Full Screen (F11)" : "Enter Full Screen (F11)"}
          >
            {isFullScreen ? "Exit Full Screen" : "Full Screen"}
          </Button>
        </header>

        {/* Full-screen coding interface */}
        <div className="flex flex-1 overflow-hidden">
          <ResizablePanelGroup direction="horizontal" className="h-full w-full">
            <ResizablePanel defaultSize={40} minSize={30}>
              <div className="h-full overflow-auto">
                <Question {...sampleQuestion} />
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={60} minSize={40}>
              <div className="h-full overflow-hidden">
                <Editor onCompile={handleCompile} onSubmit={handleSubmit} />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        {/* Footer with warning count */}
        {warningCount > 0 && (
          <div className="bg-destructive/10 text-destructive py-1 px-4 text-xs flex items-center justify-between">
            <span>{warningCount} {warningCount === 1 ? 'violation' : 'violations'} recorded in this session</span>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-6 text-xs"
              onClick={() => setWarnings([])}
            >
              Clear
            </Button>
          </div>
        )}
      </div>
    </>
  );
}