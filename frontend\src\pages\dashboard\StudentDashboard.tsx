import React, { useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Code, BookOpen, Trophy, Clock, ArrowRight } from 'lucide-react';

const StudentDashboard: React.FC = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    console.log('Student Dashboard mounted', { user });
  }, [user]);

  // Parse user metadata for personalization
  const getUserMetadata = () => {
    if (!user?.metadata) return {};
    
    try {
      return typeof user.metadata === 'string' 
        ? JSON.parse(user.metadata) 
        : user.metadata;
    } catch (error) {
      console.error('Error parsing user metadata:', error);
      return {};
    }
  };

  const metadata = getUserMetadata();
  const userName = user?.first_name || 'Student';
  const programmingLanguages = metadata.programming_languages || [];
  const experience = metadata.experience || 'intermediate';

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Student Dashboard</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 dark:text-gray-300">Welcome, {userName}</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="mb-4 md:mb-0">
                <h2 className="text-2xl font-bold mb-2">Welcome to CodeTest Platform</h2>
                <p className="text-blue-100">
                  Your personalized coding journey awaits. Start practicing and improve your skills!
                </p>
              </div>
              <Button 
                className="bg-white text-blue-600 hover:bg-blue-50"
                onClick={() => navigate('/start-coding')}
              >
                Start Coding <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Stats Overview */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Your Progress</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900 mr-4">
                  <Trophy className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Completed Challenges</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                </div>
              </div>
            </Card>
            <Card className="p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900 mr-4">
                  <Code className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Current Streak</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">0 days</p>
                </div>
              </div>
            </Card>
            <Card className="p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900 mr-4">
                  <Clock className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Time Spent Coding</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">0 hrs</p>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Recommended Challenges */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Recommended Challenges</h2>
            <Button variant="ghost" size="sm">
              View All
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((_, index) => (
              <Card key={index} className="border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 text-xs font-medium px-2.5 py-0.5 rounded">
                      {experience.charAt(0).toUpperCase() + experience.slice(1)}
                    </div>
                    <div className="text-yellow-500">⭐⭐⭐</div>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                    Challenge #{index + 1}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    This is a sample challenge description. Solve this problem to improve your coding skills.
                  </p>
                  <div className="flex justify-between items-center">
                    <div className="flex space-x-1">
                      {programmingLanguages.slice(0, 2).map((lang, i) => (
                        <span key={i} className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded">
                          {lang}
                        </span>
                      ))}
                      {programmingLanguages.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded">
                          +{programmingLanguages.length - 2}
                        </span>
                      )}
                    </div>
                    <Button size="sm">Start</Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default StudentDashboard;
