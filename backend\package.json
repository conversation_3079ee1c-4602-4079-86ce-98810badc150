{"name": "code-test-platform-backend", "version": "1.0.0", "description": "Backend for the code testing platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "dependencies": {"axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "node-fetch": "^3.3.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/node": "^20.17.50", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}