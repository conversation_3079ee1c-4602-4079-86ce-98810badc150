// backend/src/models/Question.ts

/**
 * Question difficulty levels as enum values matching WorqDB schema
 */
export enum QuestionDifficulty {
  EASY = 1,
  MEDIUM = 2,
  HARD = 3
}

/**
 * Question interface matching WorqDB table structure
 */
export interface Question {
  documentId?: string;
  createdAt?: string;
  updatedAt?: string;
  title: string;
  description: string;
  difficulty: QuestionDifficulty;
  category: string;
  constraints?: string | null;
  test_cases?: string | null;
}

/**
 * Question creation data (without auto-generated fields)
 */
export interface CreateQuestionData {
  title: string;
  description: string;
  difficulty: QuestionDifficulty;
  category: string;
  constraints?: string;
  test_cases?: string;
}

/**
 * Question update data (partial fields)
 */
export interface UpdateQuestionData {
  title?: string;
  description?: string;
  difficulty?: QuestionDifficulty;
  category?: string;
  constraints?: string;
  test_cases?: string;
}

/**
 * Question query filters
 */
export interface QuestionFilters {
  difficulty?: QuestionDifficulty;
  category?: string;
}

/**
 * Test case structure for questions
 */
export interface TestCase {
  input: any;
  output: any;
  explanation?: string;
}

/**
 * Constraint structure for questions
 */
export interface QuestionConstraints {
  time_limit?: number;
  memory_limit?: number;
  input_constraints?: string[];
  output_constraints?: string[];
}
