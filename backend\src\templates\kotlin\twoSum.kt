// Write your solution here
fun twoSum(nums: IntArray, target: Int): IntArray {
    // Your code here
    val map = mutableMapOf<Int, Int>()

    for (i in nums.indices) {
        val complement = target - nums[i]
        if (map.contains<PERSON>ey(complement)) {
            return intArrayOf(map[complement]!!, i)
        }
        map[nums[i]] = i
    }

    return intArrayOf()
}

// Main function with test cases
fun main() {
    // Test cases
    val testCase1 = intArrayOf(2, 7, 11, 15)
    val target1 = 9

    val testCase2 = intArrayOf(3, 2, 4)
    val target2 = 6

    // Run test cases
    println("Test Case 1:")
    println("Input: nums = [2, 7, 11, 15], target = 9")
    val result1 = twoSum(testCase1, target1)
    println("Output: [" + result1.joinToString(", ") + "]")
    println("---")

    println("Test Case 2:")
    println("Input: nums = [3, 2, 4], target = 6")
    val result2 = twoSum(testCase2, target2)
    println("Output: [" + result2.joinToString(", ") + "]")
    println("---")
}
