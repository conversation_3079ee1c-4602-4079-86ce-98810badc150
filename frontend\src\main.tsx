import App from './App.tsx';
import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import { PostHogProvider } from 'posthog-js/react';
import { Analytics } from '@vercel/analytics/react';
const options = {
  api_host: 'https://us.i.posthog.com',
};

// Only disable console in production
if (import.meta.env.VITE_APP_ENV === 'production') {
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
  console.info = () => {};
  console.debug = () => {};
}

function isProduction() {
  return (
    import.meta.env.VITE_APP_ENV === 'production' ||
    window.location.hostname.endsWith('.worqhat.app')
  );
}

// Initialize React immediately
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    {isProduction() ? (
      <PostHogProvider
        apiKey={import.meta.env.VITE_POSTHOG_API_KEY}
        options={{
          ...options,
          autocapture: true,
          capture_pageview: true,
          capture_pageleave: true,
        }}
      >
        <App />
        <Analytics />
      </PostHogProvider>
    ) : import.meta.env.VITE_ENABLE_ANALYTICS ? (
      <PostHogProvider
        apiKey={import.meta.env.VITE_POSTHOG_API_KEY}
        options={{
          ...options,
          autocapture: true,
          capture_pageview: true,
          capture_pageleave: true,
        }}
      >
        <App />
        <Analytics />
      </PostHogProvider>
    ) : (
      <>
        <App />
        <Analytics />
      </>
    )}
  </React.StrictMode>
);
