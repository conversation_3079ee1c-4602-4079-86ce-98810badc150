import { useAuth } from '../context/AuthContext';
import { useOrg } from '../context/OrgContext';
import { motion } from 'framer-motion';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  FileText,
  BarChart3,
  Settings,
  Plus,
  UserCheck,
  Code,
  Clock,
  TrendingUp,
  Activity
} from 'lucide-react';

export default function AdminDashboard() {
  const { user, logout } = useAuth();
  const { orgName, orgIcon } = useOrg();
  const navigate = useNavigate();

  useEffect(() => {
    console.log('📊 AdminDashboard mounted', {
      userId: user?.id,
      userName: user?.first_name,
      orgName
    });
  }, [user, orgName]);

  const handleLogout = async () => {
    console.log('👋 Admin logout initiated');
    try {
      await logout();
      console.log('✅ Admin logout completed');
    } catch (error) {
      console.error('❌ Error during admin logout:', error);
    } finally {
      // Always navigate to auth page, regardless of logout success
      navigate('/auth');
    }
  };

  const handleQuickAction = (action: string) => {
    console.log('⚡ Quick action triggered:', action);
    // TODO: Implement quick action handlers
  };

  const stats = [
    {
      title: "Active Students",
      value: "247",
      change: "+12%",
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Total Tests",
      value: "32",
      change: "+3",
      icon: FileText,
      color: "text-green-600"
    },
    {
      title: "Submissions Today",
      value: "89",
      change: "+23%",
      icon: Code,
      color: "text-purple-600"
    },
    {
      title: "Avg. Completion Time",
      value: "42m",
      change: "-8%",
      icon: Clock,
      color: "text-orange-600"
    }
  ];

  const quickActions = [
    {
      title: "Create New Test",
      description: "Design a new coding assessment",
      icon: Plus,
      color: "bg-blue-500 hover:bg-blue-600"
    },
    {
      title: "Manage Students",
      description: "View and organize student accounts",
      icon: UserCheck,
      color: "bg-green-500 hover:bg-green-600"
    },
    {
      title: "View Analytics",
      description: "Analyze performance metrics",
      icon: BarChart3,
      color: "bg-purple-500 hover:bg-purple-600"
    },
    {
      title: "System Settings",
      description: "Configure platform settings",
      icon: Settings,
      color: "bg-gray-500 hover:bg-gray-600"
    }
  ];

  const recentActivity = [
    {
      user: "John Smith",
      action: "completed JavaScript Test #3",
      time: "2 minutes ago",
      status: "success"
    },
    {
      user: "Sarah Johnson",
      action: "started Python Assessment",
      time: "5 minutes ago",
      status: "active"
    },
    {
      user: "Mike Chen",
      action: "submitted React Component Test",
      time: "8 minutes ago",
      status: "success"
    },
    {
      user: "Emma Davis",
      action: "requested help on Algorithm Challenge",
      time: "12 minutes ago",
      status: "help"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              {orgIcon}
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {orgName} Admin Dashboard
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Welcome back, {user?.first_name || 'Admin'}
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </motion.header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 + index * 0.1 }}
              className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    {stat.change}
                  </p>
                </div>
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
              </div>
            </motion.div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="lg:col-span-2"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <motion.button
                  key={action.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  onClick={() => handleQuickAction(action.title)}
                  className="p-6 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 hover:shadow-md transition-all duration-200 text-left group"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-3 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform`}>
                      <action.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="lg:col-span-1"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Recent Activity
            </h2>
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 p-6">
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    className="flex items-start space-x-3"
                  >
                    <div className={`p-2 rounded-full ${
                      activity.status === 'success' ? 'bg-green-100 text-green-600' :
                      activity.status === 'active' ? 'bg-blue-100 text-blue-600' :
                      'bg-orange-100 text-orange-600'
                    }`}>
                      <Activity className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.user}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {activity.action}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        {activity.time}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Performance Chart Placeholder */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-8"
        >
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Performance Overview
          </h2>
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 p-6">
            <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <TrendingUp className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Performance Charts</p>
                <p className="text-sm">Analytics integration coming soon</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
