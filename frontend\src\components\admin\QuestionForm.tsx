import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { IconPlus, IconTrash, IconDeviceFloppy, IconX } from '@tabler/icons-react';

interface Example {
  input: string;
  output: string;
  explanation?: string;
}

interface TestCase {
  input: any;
  expectedOutput: any;
  description?: string;
}

interface QuestionFormData {
  title: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  examples: Example[];
  constraints: string[];
  test_cases: TestCase[];
}

interface QuestionFormProps {
  initialData?: QuestionFormData;
  onSubmit: (data: QuestionFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export function QuestionForm({ initialData, onSubmit, onCancel, isLoading, mode }: QuestionFormProps) {
  const [formData, setFormData] = useState<QuestionFormData>({
    title: '',
    description: '',
    difficulty: 'Easy',
    category: '',
    examples: [{ input: '', output: '', explanation: '' }],
    constraints: [''],
    test_cases: [{ input: {}, expectedOutput: {}, description: '' }]
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.title.trim() || !formData.description.trim() || !formData.category.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    // Filter out empty examples and constraints
    const cleanedData = {
      ...formData,
      examples: formData.examples.filter(ex => ex.input.trim() && ex.output.trim()),
      constraints: formData.constraints.filter(c => c.trim()),
      test_cases: formData.test_cases.filter(tc =>
        tc.input && tc.expectedOutput &&
        (typeof tc.input === 'string' ? tc.input.trim() : true) &&
        (typeof tc.expectedOutput === 'string' ? tc.expectedOutput.trim() : true)
      )
    };

    await onSubmit(cleanedData);
  };

  const addExample = () => {
    setFormData(prev => ({
      ...prev,
      examples: [...prev.examples, { input: '', output: '', explanation: '' }]
    }));
  };

  const removeExample = (index: number) => {
    setFormData(prev => ({
      ...prev,
      examples: prev.examples.filter((_, i) => i !== index)
    }));
  };

  const updateExample = (index: number, field: keyof Example, value: string) => {
    setFormData(prev => ({
      ...prev,
      examples: prev.examples.map((ex, i) =>
        i === index ? { ...ex, [field]: value } : ex
      )
    }));
  };

  const addConstraint = () => {
    setFormData(prev => ({
      ...prev,
      constraints: [...prev.constraints, '']
    }));
  };

  const removeConstraint = (index: number) => {
    setFormData(prev => ({
      ...prev,
      constraints: prev.constraints.filter((_, i) => i !== index)
    }));
  };

  const updateConstraint = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      constraints: prev.constraints.map((c, i) => i === index ? value : c)
    }));
  };

  const addTestCase = () => {
    setFormData(prev => ({
      ...prev,
      test_cases: [...prev.test_cases, { input: {}, expectedOutput: {}, description: '' }]
    }));
  };

  const removeTestCase = (index: number) => {
    setFormData(prev => ({
      ...prev,
      test_cases: prev.test_cases.filter((_, i) => i !== index)
    }));
  };

  const updateTestCase = (index: number, field: keyof TestCase, value: any) => {
    setFormData(prev => ({
      ...prev,
      test_cases: prev.test_cases.map((tc, i) =>
        i === index ? { ...tc, [field]: value } : tc
      )
    }));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'Hard': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-2xl">
            {mode === 'create' ? 'Create New Question' : 'Edit Question'}
          </CardTitle>
          <Badge className={getDifficultyColor(formData.difficulty)}>
            {formData.difficulty}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="e.g., Two Sum"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                placeholder="e.g., Array, String, Dynamic Programming"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty *</Label>
            <Select
              value={formData.difficulty}
              onValueChange={(value: 'Easy' | 'Medium' | 'Hard') =>
                setFormData(prev => ({ ...prev, difficulty: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Easy">Easy</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="Hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe the problem in detail..."
              rows={4}
              required
            />
          </div>

          {/* Examples Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-semibold">Examples</Label>
              <Button type="button" variant="outline" size="sm" onClick={addExample}>
                <IconPlus className="h-4 w-4 mr-1" />
                Add Example
              </Button>
            </div>
            {formData.examples.map((example, index) => (
              <Card key={index} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium">Example {index + 1}</span>
                  {formData.examples.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeExample(index)}
                    >
                      <IconTrash className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <Label>Input</Label>
                    <Textarea
                      value={example.input}
                      onChange={(e) => updateExample(index, 'input', e.target.value)}
                      placeholder="nums = [2,7,11,15], target = 9"
                      rows={2}
                    />
                  </div>
                  <div>
                    <Label>Output</Label>
                    <Textarea
                      value={example.output}
                      onChange={(e) => updateExample(index, 'output', e.target.value)}
                      placeholder="[0,1]"
                      rows={2}
                    />
                  </div>
                </div>
                <div className="mt-3">
                  <Label>Explanation (Optional)</Label>
                  <Textarea
                    value={example.explanation || ''}
                    onChange={(e) => updateExample(index, 'explanation', e.target.value)}
                    placeholder="Because nums[0] + nums[1] == 9, we return [0, 1]."
                    rows={2}
                  />
                </div>
              </Card>
            ))}
          </div>

          {/* Constraints Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-semibold">Constraints</Label>
              <Button type="button" variant="outline" size="sm" onClick={addConstraint}>
                <IconPlus className="h-4 w-4 mr-1" />
                Add Constraint
              </Button>
            </div>
            {formData.constraints.map((constraint, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={constraint}
                  onChange={(e) => updateConstraint(index, e.target.value)}
                  placeholder="e.g., 2 <= nums.length <= 10^4"
                  className="flex-1"
                />
                {formData.constraints.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeConstraint(index)}
                  >
                    <IconTrash className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          {/* Test Cases Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-semibold">Test Cases</Label>
              <Button type="button" variant="outline" size="sm" onClick={addTestCase}>
                <IconPlus className="h-4 w-4 mr-1" />
                Add Test Case
              </Button>
            </div>
            {formData.test_cases.map((testCase, index) => (
              <Card key={index} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium">Test Case {index + 1}</span>
                  {formData.test_cases.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTestCase(index)}
                    >
                      <IconTrash className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <Label>Input (JSON)</Label>
                    <Textarea
                      value={typeof testCase.input === 'string' ? testCase.input : JSON.stringify(testCase.input, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          updateTestCase(index, 'input', parsed);
                        } catch {
                          updateTestCase(index, 'input', e.target.value);
                        }
                      }}
                      placeholder='{"nums": [2,7,11,15], "target": 9}'
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label>Expected Output (JSON)</Label>
                    <Textarea
                      value={typeof testCase.expectedOutput === 'string' ? testCase.expectedOutput : JSON.stringify(testCase.expectedOutput, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          updateTestCase(index, 'expectedOutput', parsed);
                        } catch {
                          updateTestCase(index, 'expectedOutput', e.target.value);
                        }
                      }}
                      placeholder='[0, 1]'
                      rows={3}
                    />
                  </div>
                </div>
                <div className="mt-3">
                  <Label>Description (Optional)</Label>
                  <Input
                    value={testCase.description || ''}
                    onChange={(e) => updateTestCase(index, 'description', e.target.value)}
                    placeholder="Basic test case with answer at beginning"
                  />
                </div>
              </Card>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onCancel}>
              <IconX className="h-4 w-4 mr-1" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <IconDeviceFloppy className="h-4 w-4 mr-1" />
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Question' : 'Update Question'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
