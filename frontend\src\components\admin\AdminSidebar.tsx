import * as React from 'react';
import {
  IconDashboard,
  IconQuestionMark,
  IconUsers,
  IconTrophy,
  IconCategory,
  IconChartBar,
  IconUserPlus,
  IconInnerShadowTop,
  IconSettings,
  IconHelp,
  IconSearch,
} from '@tabler/icons-react';

import { AdminNavUser } from '@/components/admin/AdminNavUser';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupContent,
} from '@/components/ui/sidebar';

interface AdminSidebarProps extends React.ComponentProps<typeof Sidebar> {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

// Admin navigation data
const adminNavData = {
  main: [
    {
      title: 'Dashboard',
      id: 'dashboard',
      icon: IconDashboard,
    },
    {
      title: 'Questions',
      id: 'questions',
      icon: IconQuestionMark,
    },
    {
      title: 'Students',
      id: 'students',
      icon: IconUsers,
    },
    {
      title: 'Leaderboard',
      id: 'leaderboard',
      icon: IconTrophy,
    },
    {
      title: 'Manage Categories',
      id: 'categories',
      icon: IconCategory,
    },
    {
      title: 'Reports',
      id: 'reports',
      icon: IconChartBar,
    },
    {
      title: 'Invite Students',
      id: 'invite',
      icon: IconUserPlus,
    },
  ],
  secondary: [
    {
      title: 'Settings',
      id: 'settings',
      icon: IconSettings,
    },
    {
      title: 'Help Center',
      id: 'help',
      icon: IconHelp,
    },
    {
      title: 'Search',
      id: 'search',
      icon: IconSearch,
    },
  ],
};

export function AdminSidebar({ activeSection, onSectionChange, ...props }: AdminSidebarProps) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      {/* Sidebar Header */}
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <a href="#" className="flex items-center">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">Admin Panel</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      {/* Sidebar Content */}
      <SidebarContent>
        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupContent className="flex flex-col gap-2">
            <SidebarMenu>
              {adminNavData.main.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    isActive={activeSection === item.id}
                    onClick={() => onSectionChange(item.id)}
                    className="w-full justify-start"
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Secondary Navigation */}
        <SidebarGroup className="mt-auto">
          <SidebarGroupContent>
            <SidebarMenu>
              {adminNavData.secondary.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    isActive={activeSection === item.id}
                    onClick={() => onSectionChange(item.id)}
                    className="w-full justify-start"
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      {/* Sidebar Footer */}
      <SidebarFooter>
        <AdminNavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
