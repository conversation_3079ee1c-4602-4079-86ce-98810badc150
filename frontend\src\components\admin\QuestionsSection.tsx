import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/context/AuthContext';
import { QuestionForm } from './QuestionForm';
import {
  IconPlus,
  IconUpload,
  IconSparkles,
  IconChevronDown,
  IconTrash,
  IconEdit,
  IconEye,
  IconArrowLeft,
  IconSearch,
} from '@tabler/icons-react';

interface Question {
  id: string;
  title: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  examples: string;
  constraints: string;
  test_cases: string;
  created_by: string;
  is_active: string;
  createdAt?: string;
  updatedAt?: string;
}

interface QuestionFormData {
  title: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  examples: Array<{ input: string; output: string; explanation?: string }>;
  constraints: string[];
  test_cases: Array<{ input: any; expectedOutput: any; description?: string }>;
}

export function QuestionsSection() {
  const { user } = useAuth();
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentView, setCurrentView] = useState<'list' | 'create' | 'edit' | 'preview'>('list');
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);

  // Fetch questions from API
  const fetchQuestions = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams();
      if (selectedDifficulty !== 'all') {
        params.append('difficulty', selectedDifficulty.charAt(0).toUpperCase() + selectedDifficulty.slice(1));
      }

      const response = await fetch(`http://localhost:3001/api/questions?${params}`);
      const data = await response.json();

      if (data.success) {
        setQuestions(data.data);
      } else {
        console.error('Failed to fetch questions:', data.message);
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchQuestions();
  }, [selectedDifficulty]);

  // Filter questions based on search term
  const filteredQuestions = questions.filter(q =>
    q.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    q.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get difficulty badge color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'hard':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Handle question creation
  const handleCreateQuestion = async (formData: QuestionFormData) => {
    try {
      setIsLoading(true);

      console.log('📝 Creating question with form data:', formData);
      console.log('📊 Form data details:');
      console.log('  - Examples:', formData.examples?.length || 0, 'items');
      console.log('  - Constraints:', formData.constraints?.length || 0, 'items');
      console.log('  - Test cases:', formData.test_cases?.length || 0, 'items');

      // Prepare the payload
      const payload = {
        ...formData,
        examples: formData.examples || [],
        constraints: formData.constraints || [],
        test_cases: formData.test_cases || [],
        created_by: user?.id || 'admin'
      };

      console.log('📤 Sending payload to backend:', JSON.stringify(payload, null, 2));

      const response = await fetch('http://localhost:3001/api/questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      console.log('📥 Response status:', response.status);
      const data = await response.json();
      console.log('📥 Response data:', data);

      if (data.success) {
        console.log('✅ Question created successfully:', data.data?.id);
        await fetchQuestions();
        setCurrentView('list');
        alert('Question created successfully!');
      } else {
        console.error('❌ Failed to create question:', data.message);
        alert('Failed to create question: ' + data.message);
      }
    } catch (error) {
      console.error('❌ Error creating question:', error);
      alert('Failed to create question');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle question update
  const handleUpdateQuestion = async (formData: QuestionFormData) => {
    if (!editingQuestion) return;

    try {
      setIsLoading(true);

      console.log('🔄 Updating question:', editingQuestion.id);
      console.log('📝 Update form data:', formData);
      console.log('📊 Update data details:');
      console.log('  - Examples:', formData.examples?.length || 0, 'items');
      console.log('  - Constraints:', formData.constraints?.length || 0, 'items');
      console.log('  - Test cases:', formData.test_cases?.length || 0, 'items');

      // Prepare the payload
      const payload = {
        ...formData,
        examples: formData.examples || [],
        constraints: formData.constraints || [],
        test_cases: formData.test_cases || [],
      };

      console.log('📤 Sending update payload to backend:', JSON.stringify(payload, null, 2));

      const response = await fetch(`http://localhost:3001/api/questions/${editingQuestion.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      console.log('📥 Update response status:', response.status);
      const data = await response.json();
      console.log('📥 Update response data:', data);

      if (data.success) {
        console.log('✅ Question updated successfully');
        await fetchQuestions();
        setCurrentView('list');
        setEditingQuestion(null);
        alert('Question updated successfully!');
      } else {
        console.error('❌ Failed to update question:', data.message);
        alert('Failed to update question: ' + data.message);
      }
    } catch (error) {
      console.error('❌ Error updating question:', error);
      alert('Failed to update question');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle question deletion
  const handleDeleteQuestion = async (questionId: string) => {
    if (!confirm('Are you sure you want to delete this question?')) return;

    try {
      setIsLoading(true);
      const response = await fetch(`http://localhost:3001/api/questions/${questionId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        await fetchQuestions();
        if (selectedQuestion?.id === questionId) {
          setSelectedQuestion(null);
        }
        alert('Question deleted successfully!');
      } else {
        alert('Failed to delete question: ' + data.message);
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      alert('Failed to delete question');
    } finally {
      setIsLoading(false);
    }
  };

  // Parse JSON fields for editing
  const parseQuestionForEdit = (question: Question): QuestionFormData => {
    try {
      return {
        title: question.title,
        description: question.description,
        difficulty: question.difficulty,
        category: question.category,
        examples: JSON.parse(question.examples || '[]'),
        constraints: JSON.parse(question.constraints || '[]'),
        test_cases: JSON.parse(question.test_cases || '[]'),
      };
    } catch (error) {
      console.error('Error parsing question data:', error);
      return {
        title: question.title,
        description: question.description,
        difficulty: question.difficulty,
        category: question.category,
        examples: [],
        constraints: [],
        test_cases: [],
      };
    }
  };

  // Render different views based on current state
  if (currentView === 'create') {
    return (
      <div className="px-4 lg:px-6">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => setCurrentView('list')}
            className="mb-4"
          >
            <IconArrowLeft className="h-4 w-4 mr-2" />
            Back to Questions
          </Button>
        </div>
        <QuestionForm
          mode="create"
          onSubmit={handleCreateQuestion}
          onCancel={() => setCurrentView('list')}
          isLoading={isLoading}
        />
      </div>
    );
  }

  if (currentView === 'edit' && editingQuestion) {
    return (
      <div className="px-4 lg:px-6">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => {
              setCurrentView('list');
              setEditingQuestion(null);
            }}
            className="mb-4"
          >
            <IconArrowLeft className="h-4 w-4 mr-2" />
            Back to Questions
          </Button>
        </div>
        <QuestionForm
          mode="edit"
          initialData={parseQuestionForEdit(editingQuestion)}
          onSubmit={handleUpdateQuestion}
          onCancel={() => {
            setCurrentView('list');
            setEditingQuestion(null);
          }}
          isLoading={isLoading}
        />
      </div>
    );
  }

  return (
    <div className="px-4 lg:px-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">Questions</h2>
        <div className="text-sm text-muted-foreground">
          {questions.length} total questions
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-wrap items-center gap-4 mb-6">
        {/* Search */}
        <div className="relative flex-1 max-w-sm">
          <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search questions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Difficulty Filter Buttons */}
        <div className="flex gap-2">
          <Button
            variant={selectedDifficulty === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('all')}
          >
            All
          </Button>
          <Button
            variant={selectedDifficulty === 'easy' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('easy')}
            className={selectedDifficulty === 'easy' ? 'bg-green-600 hover:bg-green-700' : ''}
          >
            Easy
          </Button>
          <Button
            variant={selectedDifficulty === 'medium' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('medium')}
            className={selectedDifficulty === 'medium' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
          >
            Medium
          </Button>
          <Button
            variant={selectedDifficulty === 'hard' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('hard')}
            className={selectedDifficulty === 'hard' ? 'bg-red-600 hover:bg-red-700' : ''}
          >
            Hard
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 ml-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentView('create')}
          >
            <IconPlus className="h-4 w-4 mr-2" />
            Add New
          </Button>
          <Button variant="outline" size="sm">
            <IconUpload className="h-4 w-4 mr-2" />
            Upload CSV
          </Button>
          <Button variant="default" size="sm">
            <IconSparkles className="h-4 w-4 mr-2" />
            Let AI Create Questions
          </Button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Questions List */}
        <div className="space-y-4">
          {/* Loading State */}
          {isLoading ? (
            <Card className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading questions...</p>
            </Card>
          ) : filteredQuestions.length === 0 ? (
            <Card className="p-8 text-center">
              <h3 className="text-lg font-medium mb-2">No Questions Available</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? 'No questions match your search criteria.' : 'No questions match the selected difficulty level.'}
              </p>
              <div className="flex gap-2 justify-center">
                {searchTerm && (
                  <Button variant="outline" onClick={() => setSearchTerm('')}>
                    Clear Search
                  </Button>
                )}
                <Button onClick={() => setSelectedDifficulty('all')}>
                  Show All Questions
                </Button>
                <Button onClick={() => setCurrentView('create')}>
                  <IconPlus className="h-4 w-4 mr-2" />
                  Create First Question
                </Button>
              </div>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredQuestions.map((question) => (
                <Card
                  key={question.id}
                  className={`p-4 cursor-pointer transition-colors hover:bg-accent ${
                    selectedQuestion?.id === question.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedQuestion(question)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{question.title}</h4>
                        <Badge className={getDifficultyColor(question.difficulty)}>
                          {question.difficulty}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Category: {question.category}
                      </p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingQuestion(question);
                          setCurrentView('edit');
                        }}
                      >
                        <IconEdit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteQuestion(question.id);
                        }}
                      >
                        <IconTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Question Preview */}
        <div className="lg:sticky lg:top-4">
          {selectedQuestion ? (
            <Card className="h-[600px] overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{selectedQuestion.title}</CardTitle>
                  <Badge className={getDifficultyColor(selectedQuestion.difficulty)}>
                    {selectedQuestion.difficulty}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">Category: {selectedQuestion.category}</p>
              </CardHeader>
              <CardContent className="h-full overflow-auto">
                <div className="space-y-4">
                  {/* Description */}
                  <div>
                    <h4 className="font-medium mb-2">Description</h4>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {selectedQuestion.description}
                    </p>
                  </div>

                  {/* Examples */}
                  {selectedQuestion.examples && (
                    <div>
                      <h4 className="font-medium mb-2">Examples</h4>
                      <div className="space-y-2">
                        {(() => {
                          try {
                            const examples = JSON.parse(selectedQuestion.examples);
                            return examples.map((example: any, index: number) => (
                              <div key={index} className="bg-muted p-3 rounded-md text-sm">
                                <div className="font-medium mb-1">Example {index + 1}:</div>
                                <div><span className="font-medium">Input:</span> {example.input}</div>
                                <div><span className="font-medium">Output:</span> {example.output}</div>
                                {example.explanation && (
                                  <div className="mt-1 text-muted-foreground">{example.explanation}</div>
                                )}
                              </div>
                            ));
                          } catch {
                            return <p className="text-sm text-muted-foreground">Invalid examples format</p>;
                          }
                        })()}
                      </div>
                    </div>
                  )}

                  {/* Constraints */}
                  {selectedQuestion.constraints && (
                    <div>
                      <h4 className="font-medium mb-2">Constraints</h4>
                      <div className="space-y-1">
                        {(() => {
                          try {
                            const constraints = JSON.parse(selectedQuestion.constraints);
                            return constraints.map((constraint: string, index: number) => (
                              <div key={index} className="text-sm text-muted-foreground">
                                • {constraint}
                              </div>
                            ));
                          } catch {
                            return <p className="text-sm text-muted-foreground">Invalid constraints format</p>;
                          }
                        })()}
                      </div>
                    </div>
                  )}

                  {/* Test Cases */}
                  {selectedQuestion.test_cases && (
                    <div>
                      <h4 className="font-medium mb-2">Test Cases</h4>
                      <div className="space-y-2">
                        {(() => {
                          try {
                            const testCases = JSON.parse(selectedQuestion.test_cases);
                            return testCases.slice(0, 3).map((testCase: any, index: number) => (
                              <div key={index} className="bg-muted p-3 rounded-md text-sm">
                                <div className="font-medium mb-1">Test Case {index + 1}:</div>
                                <div><span className="font-medium">Input:</span> {JSON.stringify(testCase.input)}</div>
                                <div><span className="font-medium">Expected:</span> {JSON.stringify(testCase.expectedOutput)}</div>
                              </div>
                            ));
                          } catch {
                            return <p className="text-sm text-muted-foreground">Invalid test cases format</p>;
                          }
                        })()}
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mt-6 pt-4 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setEditingQuestion(selectedQuestion);
                      setCurrentView('edit');
                    }}
                  >
                    <IconEdit className="h-4 w-4 mr-1" />
                    Edit Question
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setCurrentView('preview')}
                  >
                    <IconEye className="h-4 w-4 mr-1" />
                    Full Preview
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="p-6 h-[500px] flex flex-col items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mb-4">
                  <IconSparkles className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Select a Question</h3>
                <p className="text-muted-foreground">
                  Select a question to view its details and preview
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
