import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  IconPlus,
  IconUpload,
  IconSparkles,
  IconChevronDown,
  IconTrash,
} from '@tabler/icons-react';

export function QuestionsSection() {
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedQuestion, setSelectedQuestion] = useState<string | null>(null);

  // Mock questions data
  const questions = [
    {
      id: '1',
      title: 'Two Sum',
      difficulty: 'Easy',
      category: 'Array',
      successRate: 72,
    },
    {
      id: '2',
      title: 'Valid Parentheses',
      difficulty: 'Medium',
      category: 'Stack',
      successRate: 54,
    },
    {
      id: '3',
      title: 'Merge K Sorted Lists',
      difficulty: 'Hard',
      category: 'Linked List',
      successRate: 36,
    },
  ];

  // Filter questions based on difficulty
  const filteredQuestions = selectedDifficulty === 'all' 
    ? questions 
    : questions.filter(q => q.difficulty.toLowerCase() === selectedDifficulty);

  // Get difficulty badge color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'hard':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div className="px-4 lg:px-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">Questions</h2>
      </div>

      {/* Filter and Action Buttons */}
      <div className="flex flex-wrap items-center gap-4 mb-6">
        {/* Difficulty Filter Buttons */}
        <div className="flex gap-2">
          <Button
            variant={selectedDifficulty === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('all')}
          >
            All
          </Button>
          <Button
            variant={selectedDifficulty === 'easy' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('easy')}
            className={selectedDifficulty === 'easy' ? 'bg-green-600 hover:bg-green-700' : ''}
          >
            Easy
          </Button>
          <Button
            variant={selectedDifficulty === 'medium' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('medium')}
            className={selectedDifficulty === 'medium' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
          >
            Medium
          </Button>
          <Button
            variant={selectedDifficulty === 'hard' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('hard')}
            className={selectedDifficulty === 'hard' ? 'bg-red-600 hover:bg-red-700' : ''}
          >
            Hard
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 ml-auto">
          <Button variant="outline" size="sm">
            <IconPlus className="h-4 w-4 mr-2" />
            Add New
          </Button>
          <Button variant="outline" size="sm">
            <IconUpload className="h-4 w-4 mr-2" />
            Upload CSV
          </Button>
          <Button variant="default" size="sm">
            <IconSparkles className="h-4 w-4 mr-2" />
            Let AI Create Questions
          </Button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Questions List */}
        <div className="space-y-4">
          {/* Question Type Selector */}
          <div className="flex items-center gap-2 mb-4">
            <span className="text-sm font-medium">Select Question Type:</span>
            <Select>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Choose question type" />
                <IconChevronDown className="h-4 w-4" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="coding">Coding Challenge</SelectItem>
                <SelectItem value="mcq">Multiple Choice</SelectItem>
                <SelectItem value="essay">Essay Question</SelectItem>
                <SelectItem value="debug">Debug Code</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="ghost" size="sm">
              <IconPlus className="h-4 w-4" />
            </Button>
          </div>

          {/* Questions Available Message */}
          {filteredQuestions.length === 0 ? (
            <Card className="p-8 text-center">
              <h3 className="text-lg font-medium mb-2">No Questions Available</h3>
              <p className="text-muted-foreground mb-4">
                No questions match the selected difficulty level.
              </p>
              <Button onClick={() => setSelectedDifficulty('all')}>
                Show All Questions
              </Button>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredQuestions.map((question) => (
                <Card
                  key={question.id}
                  className={`p-4 cursor-pointer transition-colors hover:bg-accent ${
                    selectedQuestion === question.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedQuestion(question.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{question.title}</h4>
                        <Badge className={getDifficultyColor(question.difficulty)}>
                          {question.difficulty}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Category: {question.category} • Success Rate: {question.successRate}%
                      </p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <IconTrash className="h-4 w-4" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Question Preview */}
        <div className="lg:sticky lg:top-4">
          <Card className="p-6 h-[500px] flex flex-col items-center justify-center">
            {selectedQuestion ? (
              <div className="text-center">
                <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mb-4">
                  <IconSparkles className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Question Preview</h3>
                <p className="text-muted-foreground mb-4">
                  Selected question details would appear here for editing and preview.
                </p>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    Edit Question
                  </Button>
                  <Button size="sm">
                    Preview
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center mb-4">
                  <IconSparkles className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Select a Question</h3>
                <p className="text-muted-foreground">
                  Select a Question to Update and Preview
                </p>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
