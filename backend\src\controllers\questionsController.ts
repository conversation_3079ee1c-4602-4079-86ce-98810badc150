// backend/src/controllers/questionsController.ts

import { Request, Response } from 'express';
import questionsService from '../services/questionsService';
import { CreateQuestionData, UpdateQuestionData, QuestionDifficulty, QuestionFilters } from '../models/Question';

/**
 * Create a new question
 */
export const createQuestion = async (req: Request, res: Response) => {
  try {
    console.log('📝 Creating question request:', req.body);

    const { title, description, difficulty, category, constraints, test_cases } = req.body;

    // Validate required fields
    if (!title || !description || !difficulty || !category) {
      return res.status(400).json({
        success: false,
        message: 'Title, description, difficulty, and category are required'
      });
    }

    // Validate difficulty value
    if (![1, 2, 3].includes(difficulty)) {
      return res.status(400).json({
        success: false,
        message: 'Difficulty must be 1 (easy), 2 (medium), or 3 (hard)'
      });
    }

    const questionData: CreateQuestionData = {
      title,
      description,
      difficulty: difficulty as QuestionDifficulty,
      category,
      constraints,
      test_cases
    };

    const question = await questionsService.createQuestion(questionData);

    console.log('✅ Question created successfully');
    return res.status(201).json({
      success: true,
      message: 'Question created successfully',
      data: question
    });
  } catch (error) {
    console.error('❌ Error creating question:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all questions with optional filters
 */
export const getAllQuestions = async (req: Request, res: Response) => {
  try {
    console.log('🔍 Fetching all questions with query:', req.query);

    const filters: QuestionFilters = {};

    // Parse query parameters
    if (req.query.difficulty) {
      const difficulty = parseInt(req.query.difficulty as string);
      if ([1, 2, 3].includes(difficulty)) {
        filters.difficulty = difficulty as QuestionDifficulty;
      }
    }

    if (req.query.category) {
      filters.category = req.query.category as string;
    }



    const questions = await questionsService.getAllQuestions(filters);

    console.log('✅ Questions fetched successfully:', questions.length);
    return res.json({
      success: true,
      data: questions,
      count: questions.length
    });
  } catch (error) {
    console.error('❌ Error fetching questions:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get a question by ID
 */
export const getQuestionById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log('🔍 Fetching question by ID:', id);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Question ID is required'
      });
    }

    const question = await questionsService.getQuestionById(id);

    if (!question) {
      return res.status(404).json({
        success: false,
        message: 'Question not found'
      });
    }

    console.log('✅ Question found:', id);
    return res.json({
      success: true,
      data: question
    });
  } catch (error) {
    console.error('❌ Error fetching question:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update a question
 */
export const updateQuestion = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log('🔄 Updating question:', id, req.body);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Question ID is required'
      });
    }

    const { title, description, difficulty, category, constraints, test_cases } = req.body;

    // Validate difficulty if provided
    if (difficulty !== undefined && ![1, 2, 3].includes(difficulty)) {
      return res.status(400).json({
        success: false,
        message: 'Difficulty must be 1 (easy), 2 (medium), or 3 (hard)'
      });
    }

    const updateData: UpdateQuestionData = {};

    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (difficulty !== undefined) updateData.difficulty = difficulty as QuestionDifficulty;
    if (category !== undefined) updateData.category = category;
    if (constraints !== undefined) updateData.constraints = constraints;
    if (test_cases !== undefined) updateData.test_cases = test_cases;

    const updatedQuestion = await questionsService.updateQuestion(id, updateData);

    if (!updatedQuestion) {
      return res.status(404).json({
        success: false,
        message: 'Question not found'
      });
    }

    console.log('✅ Question updated successfully:', id);
    return res.json({
      success: true,
      message: 'Question updated successfully',
      data: updatedQuestion
    });
  } catch (error) {
    console.error('❌ Error updating question:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Delete a question
 */
export const deleteQuestion = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log('🗑️ Deleting question:', id);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Question ID is required'
      });
    }

    const deleted = await questionsService.deleteQuestion(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: 'Question not found'
      });
    }

    console.log('✅ Question deleted successfully:', id);
    return res.json({
      success: true,
      message: 'Question deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting question:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get questions by category
 */
export const getQuestionsByCategory = async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    console.log('🔍 Fetching questions by category:', category);

    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Category is required'
      });
    }

    const questions = await questionsService.getAllQuestions({ category });

    console.log('✅ Questions fetched by category:', questions.length);
    return res.json({
      success: true,
      data: questions,
      count: questions.length
    });
  } catch (error) {
    console.error('❌ Error fetching questions by category:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get questions by difficulty
 */
export const getQuestionsByDifficulty = async (req: Request, res: Response) => {
  try {
    const { difficulty } = req.params;
    console.log('🔍 Fetching questions by difficulty:', difficulty);

    const difficultyNum = parseInt(difficulty);
    if (![1, 2, 3].includes(difficultyNum)) {
      return res.status(400).json({
        success: false,
        message: 'Difficulty must be 1 (easy), 2 (medium), or 3 (hard)'
      });
    }

    const questions = await questionsService.getAllQuestions({
      difficulty: difficultyNum as QuestionDifficulty
    });

    console.log('✅ Questions fetched by difficulty:', questions.length);
    return res.json({
      success: true,
      data: questions,
      count: questions.length
    });
  } catch (error) {
    console.error('❌ Error fetching questions by difficulty:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
