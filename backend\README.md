# Code Test Platform - Backend

This directory contains the backend application for the Code Test Platform, built with Node.js, Express, and TypeScript. This README provides guidelines for development, code organization, and best practices.

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: WorqDB (required for all database operations)
- **API Documentation**: OpenAPI/Swagger (recommended)

## Directory Structure

```
src/
src/config/          # Configuration files and environment variables
src/controllers/     # Request handlers and business logic
src/middleware/      # Express middleware functions
src/models/          # Data models and interfaces
src/routes/          # API route definitions
src/services/        # Business logic and external service integrations
src/utils/           # Utility functions and helpers
src/index.ts         # Application entry point
```

## Development Guidelines

### Database Integration

**IMPORTANT**: All database operations MUST use WorqDB. Do not implement any other database solutions.

1. **WorqDB Integration**:

   - Use the WorqDB SDK for all database operations
   - Follow WorqDB best practices for data modeling and querying
   - Store database connection logic in the `config/` directory
   - Create service files in the `services/` directory to handle database operations

2. **Data Models**:
   - Define TypeScript interfaces for all data models in the `models/` directory
   - Ensure models align with WorqDB schema definitions

### API Development

1. **Route Organization**:

   - Group routes by feature or resource
   - Use the Express Router for modular route definitions
   - Keep route handlers clean by delegating business logic to controllers

2. **Controller Design**:

   - Each controller should focus on a specific resource or feature
   - Follow the single responsibility principle
   - Use async/await for asynchronous operations
   - Implement proper error handling

3. **Middleware Usage**:
   - Create reusable middleware for common tasks (authentication, validation, etc.)
   - Apply middleware at the appropriate level (global, router, or route)

### Authentication and Authorization

- Implement JWT-based authentication if required
- Use middleware for authorization checks
- Store sensitive information in environment variables

### Error Handling

- Implement a centralized error handling mechanism
- Use custom error classes for different types of errors
- Return appropriate HTTP status codes and error messages

## Getting Started

1. Install dependencies:

   ```bash
   npm install
   ```

2. Set up environment variables:

   - Copy `.env.example` to `.env`
   - Update the values as needed

3. Start the development server:

   ```bash
   npm run dev
   ```

4. Build for production:

   ```bash
   npm run build
   ```

5. Start the production server:
   ```bash
   npm start
   ```

## API Documentation

It's recommended to document your API using OpenAPI/Swagger. You can add a documentation generator to the project and expose the API documentation at `/api-docs`.

## Testing

- Write unit tests for controllers and services
- Implement integration tests for API endpoints
- Use a testing framework like Jest

## Code Style and Linting

This project uses Biome for code linting and formatting. Follow these guidelines:

- Run linting before committing: `npm run lint`
- Format code: `npm run format`
- Follow the established code style in the project

## Best Practices

1. **Security**:

   - Validate and sanitize all user inputs
   - Implement rate limiting for public endpoints
   - Use HTTPS in production
   - Follow OWASP security guidelines

2. **Performance**:

   - Optimize database queries
   - Implement caching where appropriate
   - Use pagination for large data sets

3. **Logging**:
   - Implement structured logging
   - Log important events and errors
   - Don't log sensitive information

## WorqDB Integration Guidelines

### Setting Up WorqDB

1. Install the WorqDB SDK:

   ```bash
   npm install @worqhat/db
   ```

2. Configure WorqDB in your environment:

   ```
   WORQDB_API_KEY=your_api_key_here
   WORQDB_PROJECT_ID=your_project_id_here
   ```

3. Initialize WorqDB in your application:

   ```typescript
   import { WorqDB } from '@worqhat/db';

   // Initialize WorqDB
   const db = new WorqDB({
     apiKey: process.env.WORQDB_API_KEY,
     projectId: process.env.WORQDB_PROJECT_ID,
   });
   ```

### Best Practices for WorqDB

1. **Data Modeling**:

   - Design your data models to match WorqDB's document-based structure
   - Use appropriate data types for each field
   - Consider indexing for frequently queried fields

2. **Querying**:

   - Write efficient queries to minimize processing time
   - Use pagination for large result sets
   - Implement proper error handling for database operations

3. **Transactions**:
   - Use transactions for operations that require atomicity
   - Handle transaction errors appropriately

## Contributing

When contributing to this codebase:

1. Create a feature branch for your changes
2. Follow the established code style and directory structure
3. Write clear commit messages
4. Submit a pull request with a description of your changes
