{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.6", "@stytch/react": "^19.5.0", "@stytch/vanilla-js": "^5.22.7", "@tabler/icons-react": "^3.31.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.2", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "posthog-js": "^1.236.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-resizable-panels": "^3.0.1", "react-router-dom": "^7.5.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.22.0", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.2.5", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-monaco-editor": "^1.1.0"}}