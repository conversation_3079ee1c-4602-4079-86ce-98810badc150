import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  AdminSidebar,
  AdminSiteHeader,
  QuestionsSection,
  AdminOverview,
  StudentsSection
} from '@/components/admin';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState('questions');

  useEffect(() => {
    console.log('Admin Dashboard mounted', { user });

    // Check if user is actually an admin
    if (user && user.role !== 'admin') {
      console.warn('Non-admin user accessing admin dashboard:', user);
    }
  }, [user]);

  // Render content based on active section
  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <AdminOverview />;
      case 'questions':
        return <QuestionsSection />;
      case 'students':
        return <StudentsSection />;
      case 'leaderboard':
        return (
          <div className="flex items-center justify-center h-[calc(100vh-200px)]">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-4">Leaderboard Management</h3>
              <p className="text-muted-foreground">Coming soon...</p>
            </div>
          </div>
        );
      case 'categories':
        return (
          <div className="flex items-center justify-center h-[calc(100vh-200px)]">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-4">Manage Categories</h3>
              <p className="text-muted-foreground">Coming soon...</p>
            </div>
          </div>
        );
      case 'reports':
        return (
          <div className="flex items-center justify-center h-[calc(100vh-200px)]">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-4">Reports</h3>
              <p className="text-muted-foreground">Coming soon...</p>
            </div>
          </div>
        );
      case 'invite':
        return (
          <div className="flex items-center justify-center h-[calc(100vh-200px)]">
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-4">Invite Students</h3>
              <p className="text-muted-foreground">Coming soon...</p>
            </div>
          </div>
        );
      default:
        return <QuestionsSection />;
    }
  };

  return (
    <SidebarProvider>
      <AdminSidebar
        variant="inset"
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      <SidebarInset>
        <AdminSiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {renderContent()}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default AdminDashboard;
