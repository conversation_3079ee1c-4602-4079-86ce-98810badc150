interface TestCaseResult {
  passed: boolean;
  input: string;
  expected: string;
  actual: string;
}

interface FormattedResponse {
  success: boolean;
  results: {
    passed: number;
    total: number;
    testCases: TestCaseResult[];
  };
}

export const responseFormatter = {
  formatTestResults(testResults: TestCaseResult[]): FormattedResponse {
    const total = testResults.length;

    // Normalize test results to handle array format differences
    const normalizedResults = testResults.map(result => {
      // If the expected and actual values are arrays with the same numbers but different formats,
      // mark the test as passed
      if (!result.passed && result.expected && result.actual) {
        const normalizedExpected = result.expected.replace(/[\[\]"]/g, '').replace(/\s+/g, '').trim();

        let normalizedActual = result.actual;
        if (result.actual.includes('[')) {
          normalizedActual = result.actual.replace(/[\[\]"]/g, '').replace(/\s+/g, '').trim();
        } else if (result.actual.includes('List(') || result.actual.includes('Array(')) {
          normalizedActual = result.actual
            .replace(/^(List|Array)\(/, '')
            .replace(/\)$/, '')
            .replace(/\s+/g, '')
            .trim();
        } else if (result.actual.includes('(')) {
          // Handle Racket's parentheses format
          normalizedActual = result.actual
            .replace(/[\(\)"]/g, '')
            .replace(/\s+/g, '')
            .trim();
        }

        // If the normalized values match, mark the test as passed
        if (normalizedExpected === normalizedActual) {
          return {
            ...result,
            passed: true
          };
        }
      }

      return result;
    });

    const passed = normalizedResults.filter(result => result.passed).length;

    return {
      success: passed === total,
      results: {
        passed,
        total,
        testCases: normalizedResults,
      },
    };
  },

  formatError(type: string, message: string, details?: string) {
    return {
      success: false,
      error: {
        type,
        message,
        details,
      },
    };
  },
};