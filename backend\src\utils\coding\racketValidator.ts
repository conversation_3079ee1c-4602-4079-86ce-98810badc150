/**
 * Utility to validate Racket code for common errors
 * This is used when Judge<PERSON> fails to properly handle Racket code
 */

interface RacketValidationResult {
  isValid: boolean;
  errors: string[];
}

export const racketValidator = {
  /**
   * Validates Racket code for common syntax errors and typos
   * @param code The Racket code to validate
   * @returns Validation result with errors if any
   */
  validateRacketCode(code: string): RacketValidationResult {
    const errors: string[] = [];
    
    // Check for common typos
    this.checkCommonTypos(code, errors);
    
    // Check for mismatched parentheses
    this.checkMismatchedParentheses(code, errors);
    
    // Check for undefined functions
    this.checkUndefinedFunctions(code, errors);
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },
  
  /**
   * Check for common typos in Racket code
   */
  checkCommonTypos(code: string, errors: string[]): void {
    const typos: Record<string, string> = {
      'makokle-hash': 'make-hash',
      'makehash': 'make-hash',
      'hash-ref-key': 'hash-has-key?',
      'hash-has-key': 'hash-has-key?',
      'hash-set': 'hash-set!',
      'for/fold': 'for/fold',
      'in-natural': 'in-naturals',
      'in-range': 'in-range',
      'defin': 'define',
      'let*': 'let*',
      'printf': 'printf',
      'displayln': 'displayln'
    };
    
    const lines = code.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith(';;')) continue;
      
      for (const [typo, correction] of Object.entries(typos)) {
        // Skip if the typo is actually the correct form
        if (typo === correction) continue;
        
        // Case-sensitive search for typos
        if (line.includes(typo)) {
          errors.push(`Line ${i + 1}: Possible typo: '${typo}' should be '${correction}'.`);
        }
      }
    }
  },
  
  /**
   * Check for mismatched parentheses
   */
  checkMismatchedParentheses(code: string, errors: string[]): void {
    const stack: { char: string, line: number }[] = [];
    const lines = code.split('\n');
    let lineNumber = 1;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith(';;')) {
        lineNumber++;
        continue;
      }
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        
        if (char === '(' || char === '[' || char === '{') {
          stack.push({ char, line: lineNumber });
        } else if (char === ')' || char === ']' || char === '}') {
          if (stack.length === 0) {
            errors.push(`Line ${lineNumber}: Unexpected closing '${char}'.`);
            continue;
          }
          
          const last = stack.pop()!;
          const expected = this.getMatchingParenthesis(last.char);
          
          if (char !== expected) {
            errors.push(`Line ${lineNumber}: Expected '${expected}' to match '${last.char}' from line ${last.line}, but found '${char}'.`);
          }
        }
      }
      
      lineNumber++;
    }
    
    if (stack.length > 0) {
      for (const item of stack) {
        errors.push(`Line ${item.line}: Unclosed '${item.char}'.`);
      }
    }
  },
  
  /**
   * Check for undefined functions in Racket code
   */
  checkUndefinedFunctions(code: string, errors: string[]): void {
    // Common Racket functions and forms
    const commonFunctions = [
      'define', 'lambda', 'let', 'let*', 'letrec', 'if', 'cond', 'case', 'when', 'unless',
      'and', 'or', 'not', 'eq?', 'eqv?', 'equal?', 'null?', 'pair?', 'list?', 'symbol?',
      'car', 'cdr', 'cons', 'list', 'append', 'reverse', 'map', 'filter', 'foldl', 'foldr',
      'for-each', 'apply', 'eval', 'display', 'displayln', 'printf', 'format',
      'make-hash', 'hash-set!', 'hash-ref', 'hash-has-key?', 'hash-remove!', 'hash-map',
      'hash-for-each', 'hash-keys', 'hash-values', 'hash-count',
      'for', 'for/list', 'for/vector', 'for/hash', 'for/sum', 'for/product', 'for/and', 'for/or',
      'for/fold', 'for/first', 'for/last', 'for/fold',
      'in-range', 'in-naturals', 'in-list', 'in-vector', 'in-hash', 'in-string',
      'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth', 'ninth', 'tenth',
      'rest', 'take', 'drop', 'split-at', 'partition',
      '+', '-', '*', '/', 'quotient', 'remainder', 'modulo', 'add1', 'sub1',
      '=', '<', '>', '<=', '>=', 'zero?', 'positive?', 'negative?', 'even?', 'odd?',
      'max', 'min', 'abs', 'floor', 'ceiling', 'round', 'truncate',
      'string-append', 'string-length', 'string-ref', 'string=?', 'string<?', 'string>?',
      'substring', 'string-replace', 'string-split', 'string-join',
      'vector', 'vector-ref', 'vector-set!', 'vector-length', 'vector->list', 'list->vector',
      'struct', 'struct-copy', 'struct-type?', 'struct-type-info',
      'begin', 'begin0', 'void', 'values', 'call-with-values',
      'error', 'raise', 'raise-argument-error', 'with-handlers',
      'require', 'provide', 'module', 'module+', 'module*',
      'read', 'read-line', 'read-string', 'read-bytes', 'read-char',
      'write', 'write-string', 'write-bytes', 'write-char',
      'open-input-file', 'open-output-file', 'close-input-port', 'close-output-port',
      'current-input-port', 'current-output-port', 'current-error-port',
      'file-exists?', 'directory-exists?', 'delete-file', 'rename-file',
      'path?', 'path-string?', 'path->string', 'string->path',
      'system', 'system*', 'process', 'process*',
      'thread', 'thread-wait', 'thread-sleep', 'thread-send', 'thread-receive',
      'semaphore', 'semaphore-post', 'semaphore-wait', 'semaphore-try-wait?',
      'channel', 'channel-put', 'channel-get', 'channel-try-get',
      'parameterize', 'make-parameter', 'current-directory', 'current-namespace',
      'syntax', 'syntax-e', 'syntax->datum', 'datum->syntax',
      'macro', 'define-syntax', 'syntax-rules', 'syntax-case', 'syntax-parse',
      'match', 'match-define', 'match-let', 'match-let*',
      'class', 'object', 'send', 'super-new', 'this', 'new',
      'interface', 'mixin', 'trait', 'abstract', 'final', 'override',
      'public', 'private', 'protected', 'inherit', 'inherit-field',
      'member', 'field', 'init-field', 'init-rest', 'get-field', 'set-field!',
      'method', 'abstract-method', 'define-method', 'override-method',
      'super', 'inner', 'this%', 'super%',
      'define-values', 'let-values', 'let*-values', 'letrec-values',
      'call-with-input-file', 'call-with-output-file',
      'with-input-from-file', 'with-output-to-file',
      'hash', 'hasheq', 'hasheqv', 'make-immutable-hash', 'make-immutable-hasheq', 'make-immutable-hasheqv',
      'hash-copy', 'hash-clear', 'hash-clear!', 'hash-iterate-first', 'hash-iterate-next',
      'hash-iterate-key', 'hash-iterate-value', 'hash-iterate-key+value',
      'hash-update', 'hash-update!', 'hash-union', 'hash-union!',
      'hash-intersect', 'hash-intersect!', 'hash-subtract', 'hash-subtract!',
      'null'
    ];
    
    // Extract function calls from the code
    const functionCallRegex = /\(([a-zA-Z0-9\-\?!]+)/g;
    let match;
    
    while ((match = functionCallRegex.exec(code)) !== null) {
      const functionName = match[1];
      
      // Skip if it's a common Racket function
      if (commonFunctions.includes(functionName)) {
        continue;
      }
      
      // Check if the function is defined in the code
      const definitionRegex = new RegExp(`\\(define\\s+\\(${functionName}|\\(define\\s+${functionName}\\s+`, 'g');
      if (!definitionRegex.test(code)) {
        // Check if it's a typo of a common function
        const similarFunctions = this.findSimilarFunctions(functionName, commonFunctions);
        if (similarFunctions.length > 0) {
          errors.push(`Function '${functionName}' is not defined. Did you mean '${similarFunctions[0]}'?`);
        } else {
          errors.push(`Function '${functionName}' is not defined.`);
        }
      }
    }
  },
  
  /**
   * Get the matching closing parenthesis for an opening parenthesis
   */
  getMatchingParenthesis(char: string): string {
    switch (char) {
      case '(': return ')';
      case '[': return ']';
      case '{': return '}';
      default: return '';
    }
  },
  
  /**
   * Find similar functions to a potentially misspelled one
   */
  findSimilarFunctions(functionName: string, validFunctions: string[]): string[] {
    return validFunctions.filter(validFunction => {
      // Simple similarity check - at least 60% of characters match
      let matches = 0;
      const minLength = Math.min(functionName.length, validFunction.length);
      const maxLength = Math.max(functionName.length, validFunction.length);
      
      for (let i = 0; i < minLength; i++) {
        if (functionName[i].toLowerCase() === validFunction[i].toLowerCase()) matches++;
      }
      
      return matches / maxLength > 0.6;
    });
  }
};
