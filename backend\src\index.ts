import express from 'express';
import cors from 'cors';
import compileRouter from './routes/coding/compile';
import submitRouter from './routes/coding/submit';
import customTestRouter from './routes/coding/customTest';
import authRouter from './routes/auth';
import questionsRouter from './routes/questions';

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'], // Allow frontend origins
  credentials: true
}));
app.use(express.json());

// Routes
app.use('/api/coding/compile', compileRouter);
app.use('/api/coding/submit', submitRouter);
app.use('/api/coding/custom-test', customTestRouter);
app.use('/auth', authRouter); // Auth routes
app.use('/api/questions', questionsRouter); // Questions routes

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ Server error:', err);
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
app.listen(port, () => {
  console.log(`🚀 Code Test Platform Backend is running on port ${port}`);
  console.log(`📡 Health check: http://localhost:${port}/health`);
});

export default app;
