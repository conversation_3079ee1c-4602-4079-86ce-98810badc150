import React from 'react';
import { useAuth } from '../context/AuthContext';

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white shadow rounded-lg p-6">
            <h1 className="text-2xl font-semibold text-gray-900 mb-4">
              Welcome, {user?.first_name || 'Student'}!
            </h1>
            <div className="border-t border-gray-200 pt-4">
              <p className="text-gray-600">
                This is your student dashboard. More features coming soon!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard; 