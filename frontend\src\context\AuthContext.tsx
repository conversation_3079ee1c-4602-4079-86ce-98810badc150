/* eslint-disable react-refresh/only-export-components */
import { createContext, useContext, useState, useEffect, ReactNode, memo } from 'react';
import { useStytch, useStytchUser } from '@stytch/react';
import axios from 'axios';

// API base URL
const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

interface UserDetails {
  id: string;
  email: string;
  role: 'student' | 'admin';
  is_active: boolean;
  stytch_user_id: string;
  first_name?: string;
  last_name?: string;
  profile_picture?: string;
  onboardingComplete?: boolean;
  metadata?: string | Record<string, any>;
}

interface AuthContextType {
  user: UserDetails | null;
  loading: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  completeOnboarding: () => Promise<void>;
  logout: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  isLoading: true,
  isAuthenticated: false,
  error: null,
  completeOnboarding: async () => {},
  logout: async () => {},
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const stytch = useStytch();
  const { user: stytchUser } = useStytchUser();

  const isAuthenticated = !!user && !!stytchUser;

  const updateAuthState = async () => {
    try {
      console.log('⚙️ Starting auth state update...');

      if (stytchUser) {
        console.log('🔐 Stytch user found', {
          userId: stytchUser.user_id,
          email: stytchUser.emails[0]?.email
        });

        try {
          // Try to fetch existing user
          console.log('📡 Fetching user data from:', `${API_URL}/auth/user/${stytchUser.user_id}`);
          const userResponse = await axios.get(`${API_URL}/auth/user/${stytchUser.user_id}`);

          if (userResponse.data.success) {
            console.log('✅ User data fetched successfully');

            // Process user data
            const userData = userResponse.data.data;

            // Check if metadata exists and parse it if it's a string
            if (userData.metadata && typeof userData.metadata === 'string') {
              try {
                const parsedMetadata = JSON.parse(userData.metadata);

                // Set onboardingComplete flag based on metadata
                if (parsedMetadata.onboardingComplete !== undefined) {
                  userData.onboardingComplete = parsedMetadata.onboardingComplete;
                }
              } catch (parseError) {
                console.error('Error parsing user metadata:', parseError);
              }
            }

            setUser(userData);
            setError(null);
            return;
          }
        } catch (error: any) {
          console.log('🔍 Axios error details:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url,
            method: error.config?.method
          });

          if (error.response?.status === 404) {
            console.log('🔄 User not found, attempting registration');

            // Register new user
            console.log('📡 Registering new user at:', `${API_URL}/auth/register`);
            const registerResponse = await axios.post(`${API_URL}/auth/register`, {
              email: stytchUser.emails[0]?.email,
              stytch_user_id: stytchUser.user_id,
              first_name: stytchUser.name?.first_name,
              last_name: stytchUser.name?.last_name,
              profile_picture: stytchUser.providers?.[0]?.profile_picture_url
            });

            if (registerResponse.data.success) {
              console.log('✅ User registered successfully');
              setUser(registerResponse.data.data);
              setError(null);
              return;
            }
          }
        }

        // If we get here, something went wrong
        console.error('❌ Error in auth flow');
        setError('Failed to authenticate user');
        setUser(null);
      } else {
        console.log('🔒 No active user');
        setUser(null);
        setError(null);
      }
    } catch (error: any) {
      console.error('❌ Auth state update error:', error);
      setError('Authentication error');
      setUser(null);
    } finally {
      console.log('🏁 Auth state update complete', {
        isAuthenticated: !!user,
        hasUser: !!user,
        error
      });
      setLoading(false);
    }
  };

  useEffect(() => {
    updateAuthState();
  }, [stytchUser]);

  const completeOnboarding = async () => {
    if (!user) {
      console.error('Cannot complete onboarding: No user available');
      return;
    }

    console.log('Completing onboarding for user:', user.id);

    try {
      // First try the dedicated onboarding endpoint
      try {
        const response = await axios.put(`${API_URL}/auth/onboarding/${user.id}`);
        console.log('Onboarding API response:', response.data);

        if (response.data.success) {
          console.log('Successfully completed onboarding via API');
          setUser(prev => prev ? { ...prev, onboardingComplete: true } : null);
          return;
        }
      } catch (apiError) {
        console.warn('Onboarding API failed, falling back to user update:', apiError);
      }

      // Fallback: Update user metadata directly
      const metadata = user.metadata ?
        (typeof user.metadata === 'string' ? JSON.parse(user.metadata) : user.metadata) :
        {};

      const updatedMetadata = {
        ...metadata,
        onboardingComplete: true
      };

      console.log('Updating user metadata with:', updatedMetadata);

      const response = await axios.put(`${API_URL}/auth/user/${user.id}`, {
        metadata: JSON.stringify(updatedMetadata)
      });

      console.log('User update response:', response.data);

      if (response.data.success) {
        console.log('Successfully completed onboarding via user update');

        // Update the user state with the new metadata
        const updatedUser = {
          ...user,
          onboardingComplete: true,
          metadata: JSON.stringify(updatedMetadata)
        };

        console.log('Updated user state:', updatedUser);
        setUser(updatedUser);

        // Force a refresh of the auth state to ensure everything is up to date
        setTimeout(() => {
          updateAuthState();
        }, 500);
      }
    } catch (error) {
      console.error('Failed to update onboarding status:', error);
      setError('Failed to update onboarding status');
    }
  };

  // Logout function to clear authentication state
  const logout = async () => {
    console.log('🚪 Logout initiated');
    try {
      // 1. Log the user out of Stytch
      try {
        await stytch.session.revoke();
        console.log('✅ Stytch session revoked');
      } catch (stytchError) {
        console.error('⚠️ Stytch logout error, but continuing:', stytchError);
        // Continue with logout even if Stytch revoke fails
      }

      // 2. Update last login in backend if user exists
      if (user && user.id) {
        try {
          await axios.put(`${API_URL}/auth/user/${user.id}/logout`);
          console.log('✅ Backend logout successful');
        } catch (backendError) {
          console.error('⚠️ Backend logout error, but continuing:', backendError);
          // Continue with logout even if backend update fails
        }
      }

      // 3. Clear local user state
      setUser(null);
      setError(null);

      console.log('🏁 Logout completed successfully');

      // 4. Redirect to login page (handled by components using this function)
      return Promise.resolve();
    } catch (error) {
      console.error('❌ Logout error:', error);
      // Still clear the local state even if there was an error
      setUser(null);
      setError(null);
      return Promise.resolve(); // Resolve instead of reject to ensure navigation happens
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        isLoading: loading,
        isAuthenticated,
        error,
        completeOnboarding,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
