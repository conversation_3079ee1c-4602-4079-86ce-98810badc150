import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  IconUsers,
  IconQuestionMark,
  IconTrophy,
  IconChartBar,
  IconTrendingUp,
  IconEye,
} from '@tabler/icons-react';

export function AdminOverview() {
  // Mock data for admin dashboard
  const stats = {
    totalStudents: 1247,
    activeStudents: 892,
    totalQuestions: 156,
    totalSubmissions: 8934,
    avgCompletionRate: 73,
    newStudentsThisWeek: 45,
  };

  const recentActivity = [
    {
      id: 1,
      type: 'submission',
      user: '<PERSON>',
      action: 'submitted solution for "Two Sum"',
      time: '2 minutes ago',
      status: 'success',
    },
    {
      id: 2,
      type: 'registration',
      user: '<PERSON>',
      action: 'registered as new student',
      time: '15 minutes ago',
      status: 'info',
    },
    {
      id: 3,
      type: 'completion',
      user: '<PERSON>',
      action: 'completed "Array Challenges" category',
      time: '1 hour ago',
      status: 'success',
    },
    {
      id: 4,
      type: 'question',
      user: 'Admin',
      action: 'added new question "Binary Search Tree"',
      time: '2 hours ago',
      status: 'info',
    },
  ];

  const topPerformers = [
    { rank: 1, name: 'Alice Chen', score: 2450, solved: 89 },
    { rank: 2, name: 'David Kim', score: 2380, solved: 85 },
    { rank: 3, name: '<PERSON>', score: 2290, solved: 82 },
    { rank: 4, name: 'Mike <PERSON>', score: 2180, solved: 78 },
    { rank: 5, name: 'Lisa Garcia', score: 2120, solved: 75 },
  ];

  return (
    <div className="px-4 lg:px-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">Dashboard Overview</h2>
        <Button variant="outline" size="sm">
          <IconEye className="h-4 w-4 mr-2" />
          View Reports
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <IconUsers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalStudents.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <IconTrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +{stats.newStudentsThisWeek} this week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Students</CardTitle>
            <IconUsers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeStudents.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">
              {Math.round((stats.activeStudents / stats.totalStudents) * 100)}% of total
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Questions</CardTitle>
            <IconQuestionMark className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalQuestions}</div>
            <div className="text-xs text-muted-foreground">
              Across all categories
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submissions</CardTitle>
            <IconChartBar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSubmissions.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">
              {stats.avgCompletionRate}% success rate
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.status === 'success' ? 'bg-green-500' : 'bg-blue-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.user}</p>
                    <p className="text-sm text-muted-foreground">{activity.action}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4" size="sm">
              View All Activity
            </Button>
          </CardContent>
        </Card>

        {/* Top Performers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconTrophy className="h-5 w-5" />
              Top Performers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topPerformers.map((performer) => (
                <div key={performer.rank} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant={performer.rank <= 3 ? 'default' : 'secondary'}>
                      #{performer.rank}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">{performer.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {performer.solved} problems solved
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold">{performer.score}</p>
                    <p className="text-xs text-muted-foreground">points</p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4" size="sm">
              View Leaderboard
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
