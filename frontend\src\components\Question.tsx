import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';

interface Example {
  input: string;
  output: string;
  explanation?: string;
}

interface QuestionProps {
  title: string;
  description: string;
  examples: Example[];
  constraints: string[];
}

export function Question({ title, description, examples, constraints }: QuestionProps) {
  const [activeTab, setActiveTab] = useState('description');

  return (
    <Card className="w-full h-full flex flex-col border-0 rounded-none shadow-none">
      <CardHeader className="pb-2 flex flex-col">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold">{title}</CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">Easy</span>
          </div>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-6">
          <TabsList className="w-full justify-start border-b rounded-none bg-transparent h-auto p-0 mb-4">
            <TabsTrigger
              value="description"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent px-4 py-2"
            >
              Description
            </TabsTrigger>
            <TabsTrigger
              value="solution"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent px-4 py-2"
            >
              Solution
            </TabsTrigger>
            <TabsTrigger
              value="discussion"
              className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent px-4 py-2"
            >
              Discussion
            </TabsTrigger>
          </TabsList>
        </div>

        <CardContent className="flex-1 overflow-auto pt-0">
          <TabsContent value="description" className="mt-0 h-full">
            <div className="space-y-6">
              {/* Description */}
              <div className="space-y-3">
                <p className="text-foreground whitespace-pre-wrap leading-relaxed">{description}</p>
              </div>

              {/* Examples */}
              <div className="space-y-4">
                <h3 className="text-base font-semibold">Examples</h3>
                {examples.map((example, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex gap-2">
                      <span className="font-medium">Example {index + 1}:</span>
                    </div>
                    <div className="bg-muted p-3 rounded-md space-y-2 font-mono text-sm">
                      <div>
                        <span className="font-medium text-gray-500 dark:text-gray-400">Input: </span>
                        <span>{example.input}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500 dark:text-gray-400">Output: </span>
                        <span>{example.output}</span>
                      </div>
                      {example.explanation && (
                        <div>
                          <span className="font-medium text-gray-500 dark:text-gray-400">Explanation: </span>
                          <span className="text-muted-foreground">{example.explanation}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Constraints */}
              <div className="space-y-2">
                <h3 className="text-base font-semibold">Constraints</h3>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground pl-2">
                  {constraints.map((constraint, index) => (
                    <li key={index}>{constraint}</li>
                  ))}
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="solution" className="mt-0">
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">Solution will be available after you submit your code.</p>
            </div>
          </TabsContent>

          <TabsContent value="discussion" className="mt-0">
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">No discussions yet. Be the first to start a discussion!</p>
            </div>
          </TabsContent>
        </CardContent>
      </Tabs>
    </Card>
  );
}