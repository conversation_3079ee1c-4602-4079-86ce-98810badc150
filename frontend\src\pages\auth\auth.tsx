import { LoginForm } from '@/components/login-form';
import { useOrg } from '@/context/OrgContext';

export default function LoginPage() {
  const { orgName, orgLogo, orgIcon } = useOrg();

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        {orgLogo ? (
          <div className="text-center">
            <img src={orgLogo} alt={orgName} className="h-12 mx-auto mb-2" />
          </div>
        ) : (
          <a href="#" className="flex items-center gap-2 self-center font-medium">
            {orgIcon}
            {orgName}
          </a>
        )}
        <LoginForm />
      </div>
    </div>
  );
}
