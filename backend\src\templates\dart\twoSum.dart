// Write your solution here
List<int> solution(List<int> nums, int target) {
  // Your code here
  final map = <int, int>{};

  for (var i = 0; i < nums.length; i++) {
    final complement = target - nums[i];
    if (map.contains<PERSON>ey(complement)) {
      return [map[complement]!, i];
    }
    map[nums[i]] = i;
  }

  return [];
}

void main() {
  // Test cases
  final testCases = [
    {'input': [2, 7, 11, 15], 'target': 9},
    {'input': [3, 2, 4], 'target': 6}
  ];

  // Run test cases
  for (var i = 0; i < testCases.length; i++) {
    final input = List<int>.from(testCases[i]['input']);
    final target = testCases[i]['target'];
    final result = solution(input, target);

    print('Test Case ${i + 1}:');
    print('Input: nums = ${input}, target = ${target}');
    print('Output: ${result}');
    print('---');
  }
}
