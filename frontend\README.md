# Code Test Platform - Frontend

This directory contains the frontend application for the Code Test Platform, built with React, TypeScript, and Vite. This README provides guidelines for development, code organization, and best practices.

## Tech Stack

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: CSS (with potential for CSS-in-JS or Tailwind CSS)
- **Linting**: ESLint + Biome

## Directory Structure

```
src/
├── assets/          # Static assets like images, fonts, etc.
├── components/      # Reusable UI components
│   ├── common/      # Shared components (buttons, inputs, etc.)
│   ├── layout/      # Layout components (header, footer, etc.)
│   └── specific/    # Feature-specific components
├── hooks/           # Custom React hooks
├── lib/             # Utility functions and libraries
├── pages/           # Page components for each route
├── services/        # API services and data fetching logic
├── store/           # State management (if needed)
├── styles/          # Global styles and theme definitions
├── types/           # TypeScript type definitions
├── App.tsx          # Main application component
├── main.tsx         # Entry point
└── index.css        # Global CSS
```

## Development Guidelines

### Backend Integration

**Important**: For most backend use cases, use WorqHat workflows. Only use the custom backend implementation in the `/backend` folder if the functionality cannot be implemented using WorqHat workflows.

### Component Development

1. **Component Organization**:

   - Create small, focused components with a single responsibility
   - Use the appropriate directory based on the component's purpose
   - For complex components, create a dedicated folder with index.tsx as the entry point

2. **Naming Conventions**:

   - Use PascalCase for component files and folders (e.g., `Button.tsx`, `UserProfile/`)
   - Use camelCase for utility functions and hooks (e.g., `useAuth.ts`, `formatDate.ts`)

3. **Type Safety**:
   - Define proper interfaces for component props
   - Use TypeScript's type system to ensure type safety
   - Avoid using `any` type whenever possible

### State Management

- For simple state, use React's built-in `useState` and `useContext`
- For complex state, consider using a state management library if needed
- Share state between components using context or props

### API Integration

1. **WorqHat Workflows**:

   - Use WorqHat workflows for most backend functionality
   - Implement API calls to WorqHat services in the `services/` directory

2. **Custom Backend**:
   - Only use the custom backend when WorqHat workflows cannot solve the problem
   - Keep API service functions in the `services/` directory

### Performance Considerations

- Use React.memo for expensive components
- Implement proper dependency arrays in useEffect and other hooks
- Lazy load components and routes when appropriate
- Optimize bundle size by avoiding unnecessary dependencies

## Getting Started

1. Install dependencies:

   ```bash
   npm install
   ```

2. Start the development server:

   ```bash
   npm run dev
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## Code Style and Linting

This project uses ESLint and Biome for code linting and formatting. Follow these guidelines:

- Run linting before committing: `npm run lint`
- Format code: `npm run format`
- Follow the established code style in the project

## Best Practices

1. **Code Quality**:

   - Write clean, readable, and maintainable code
   - Add comments for complex logic
   - Use meaningful variable and function names

2. **Testing**:

   - Write tests for critical components and functionality
   - Ensure good test coverage

3. **Accessibility**:

   - Ensure components are accessible
   - Use semantic HTML elements
   - Include proper ARIA attributes when necessary

4. **Responsive Design**:
   - Design components to work across different screen sizes
   - Use responsive design principles

## Contributing

When contributing to this codebase:

1. Create a feature branch for your changes
2. Follow the established code style and directory structure
3. Write clear commit messages
4. Submit a pull request with a description of your changes
