// Test script for Questions API
// Run this with: node test-questions-api.js

const API_BASE = 'http://localhost:3001/api/questions';

// Sample question data
const sampleQuestion = {
  title: "Two Sum",
  description: "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice. You can return the answer in any order.",
  difficulty: 1, // Easy
  category: "Arrays",
  constraints: JSON.stringify({
    time_limit: 60,
    input_constraints: ["1 <= nums.length <= 10^4", "-10^9 <= nums[i] <= 10^9", "-10^9 <= target <= 10^9"],
    output_constraints: ["Return array of two indices"]
  }),
  test_cases: JSON.stringify([
    {
      input: "nums = [2,7,11,15], target = 9",
      output: "[0,1]",
      explanation: "Because nums[0] + nums[1] == 9, we return [0, 1]."
    },
    {
      input: "nums = [3,2,4], target = 6", 
      output: "[1,2]",
      explanation: "Because nums[1] + nums[2] == 6, we return [1, 2]."
    }
  ]),
  created_by: "admin"
};

async function testCreateQuestion() {
  try {
    console.log('🧪 Testing CREATE question...');
    
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sampleQuestion)
    });
    
    const data = await response.json();
    console.log('✅ CREATE Response:', data);
    
    if (data.success && data.data) {
      return data.data.documentId;
    }
    return null;
  } catch (error) {
    console.error('❌ CREATE Error:', error);
    return null;
  }
}

async function testGetAllQuestions() {
  try {
    console.log('🧪 Testing GET ALL questions...');
    
    const response = await fetch(API_BASE);
    const data = await response.json();
    console.log('✅ GET ALL Response:', data);
    
    return data;
  } catch (error) {
    console.error('❌ GET ALL Error:', error);
    return null;
  }
}

async function testGetQuestionById(questionId) {
  try {
    console.log('🧪 Testing GET question by ID:', questionId);
    
    const response = await fetch(`${API_BASE}/${questionId}`);
    const data = await response.json();
    console.log('✅ GET BY ID Response:', data);
    
    return data;
  } catch (error) {
    console.error('❌ GET BY ID Error:', error);
    return null;
  }
}

async function testUpdateQuestion(questionId) {
  try {
    console.log('🧪 Testing UPDATE question:', questionId);
    
    const updateData = {
      title: "Two Sum - Updated",
      description: "Updated description for Two Sum problem"
    };
    
    const response = await fetch(`${API_BASE}/${questionId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    
    const data = await response.json();
    console.log('✅ UPDATE Response:', data);
    
    return data;
  } catch (error) {
    console.error('❌ UPDATE Error:', error);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Questions API Tests...\n');
  
  // Test 1: Create a question
  const questionId = await testCreateQuestion();
  if (!questionId) {
    console.error('❌ Failed to create question, stopping tests');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: Get all questions
  await testGetAllQuestions();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 3: Get question by ID
  await testGetQuestionById(questionId);
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 4: Update question
  await testUpdateQuestion(questionId);
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 5: Get updated question
  await testGetQuestionById(questionId);
  
  console.log('\n🎉 All tests completed!');
}

// Run the tests
runTests().catch(console.error);
