interface ParsedCode {
  userCode: string;
  testCases: TestCase[];
}

interface TestCase {
  input: any;
  expectedOutput: any;
  description?: string;
}

export const codeParser = {
  parseCode(code: string, language: string): ParsedCode {
    try {
      console.log('Parsing code for language:', language);
      console.log('Original code:', code);

      // Extract test cases based on language
      let testCases: TestCase[] = [];

      switch (language) {
        case 'javascript':
          // Existing JavaScript parsing
          const jsTestCasesMatch = code.match(/const testCases = \[([\s\S]*?)\];/);
          if (jsTestCasesMatch) {
            const testCasesStr = jsTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);
            testCases = this.parseJavaScriptTestCases(testCaseMatches);
          }
          break;

        case 'python':
          // Existing Python parsing
          const pyTestCasesMatch = code.match(/test_cases\s*=\s*\[([\s\S]*?)\]/);
          if (pyTestCasesMatch) {
            const testCasesStr = pyTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);
            testCases = this.parsePythonTestCases(testCaseMatches);
          }
          break;

        case 'java':
          // Existing Java parsing
          const javaTestCasesMatch = code.match(/int\[\]\[\]\s*testCases\s*=\s*\{([\s\S]*?)\};/);
          if (javaTestCasesMatch) {
            const testCasesStr = javaTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);
            testCases = this.parseJavaTestCases(testCaseMatches);
          }
          break;

        case 'cpp':
          // Existing C++ parsing
          const cppTestCasesMatch = code.match(/vector<vector<int>>\s*testCases\s*=\s*\{([\s\S]*?)\};/);
          if (cppTestCasesMatch) {
            const testCasesStr = cppTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);
            testCases = this.parseCppTestCases(testCaseMatches);
          }
          break;

        case 'c':
          // C parsing
          const cTestCasesMatch = code.match(/int testCases\[.*?\]\[.*?\]\s*=\s*\{([\s\S]*?)\};/);
          if (cTestCasesMatch) {
            const testCasesStr = cTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);
            testCases = this.parseCTestCases(testCaseMatches);
          }
          break;

        case 'php':
          // PHP parsing
          const phpTestCasesMatch = code.match(/\$testCases\s*=\s*\[([\s\S]*?)\];/);
          if (phpTestCasesMatch) {
            const testCasesStr = phpTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\["input"\s*=>\s*\[([\s\S]*?)\],\s*"target"\s*=>\s*(\d+)\]/g);
            testCases = this.parsePhpTestCases(testCaseMatches);
          }
          break;

        case 'kotlin':
          // Kotlin parsing - using hardcoded test cases since we're using a simpler format now
          testCases.push(
            {
              input: [2, 7, 11, 15],
              expectedOutput: [0, 1]
            },
            {
              input: [3, 2, 4],
              expectedOutput: [1, 2]
            }
          );
          break;

        case 'swift':
          // Swift parsing
          const swiftTestCasesMatch = code.match(/let testCases\s*=\s*\[([\s\S]*?)\]/);
          if (swiftTestCasesMatch) {
            const testCasesStr = swiftTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\(([\s\S]*?)\)/g);
            testCases = this.parseSwiftTestCases(testCaseMatches);
          }
          break;

        case 'dart':
          // Dart parsing - using hardcoded test cases similar to Kotlin
          testCases.push(
            {
              input: [2, 7, 11, 15],
              expectedOutput: [0, 1]
            },
            {
              input: [3, 2, 4],
              expectedOutput: [1, 2]
            }
          );
          break;
      }

      console.log(`Extracted test cases for ${language}:`, testCases);

      return {
        userCode: code,
        testCases,
      };
    } catch (error) {
      console.error('Error parsing code:', error);
      throw new Error('Failed to parse code');
    }
  },

  // Helper methods for parsing test cases
  parseJavaScriptTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      const testCaseStr = match[1];
      const inputMatch = testCaseStr.match(/input: (\[.*?\])/);
      const targetMatch = testCaseStr.match(/target: (\d+)/);

      if (inputMatch && targetMatch) {
        try {
          const input = JSON.parse(inputMatch[1]);
          testCases.push({
            input,
            expectedOutput: expectedOutputs[index] || [],
          });
          index++;
        } catch (error) {
          console.error('Error parsing JavaScript test case:', error);
        }
      }
    }
    return testCases;
  },

  parsePythonTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      const testCaseStr = match[1];
      const inputMatch = testCaseStr.match(/"input":\s*(\[.*?\])/);
      const targetMatch = testCaseStr.match(/"target":\s*(\d+)/);

      if (inputMatch && targetMatch) {
        try {
          const input = JSON.parse(inputMatch[1]);
          testCases.push({
            input,
            expectedOutput: expectedOutputs[index] || [],
          });
          index++;
        } catch (error) {
          console.error('Error parsing Python test case:', error);
        }
      }
    }
    return testCases;
  },

  parseJavaTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      const testCaseStr = match[1];
      const numbers = testCaseStr.split(',').map(Number);
      testCases.push({
        input: numbers,
        expectedOutput: expectedOutputs[index] || [],
      });
      index++;
    }
    return testCases;
  },

  parseCppTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      const testCaseStr = match[1];
      const numbers = testCaseStr.split(',').map(Number);
      testCases.push({
        input: numbers,
        expectedOutput: expectedOutputs[index] || [],
      });
      index++;
    }
    return testCases;
  },

  parseCTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      const testCaseStr = match[1];
      const numbers = testCaseStr.split(',').map(Number);
      testCases.push({
        input: numbers,
        expectedOutput: expectedOutputs[index] || [],
      });
      index++;
    }
    return testCases;
  },

  parsePhpTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      try {
        // match[1] contains the input array, match[2] contains the target (not used here)
        const inputStr = match[1];

        // Parse the input array
        const input = inputStr.split(',').map(Number);

        testCases.push({
          input,
          expectedOutput: expectedOutputs[index] || [],
        });

        index++;
      } catch (error) {
        console.error('Error parsing PHP test case:', error);
      }
    }

    console.log('Parsed PHP test cases:', testCases);
    return testCases;
  },

  parseKotlinTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      try {
        // match[1] contains the input array numbers, match[2] contains the target
        const inputStr = match[1];

        // Parse the input array
        const input = inputStr.split(',').map(s => parseInt(s.trim()));

        testCases.push({
          input,
          expectedOutput: expectedOutputs[index] || [],
        });

        index++;
        console.log('Added Kotlin test case:', { input, expectedOutput: expectedOutputs[index - 1] });
      } catch (error) {
        console.error('Error parsing Kotlin test case:', error);
      }
    }

    return testCases;
  },

  parseSwiftTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      const testCaseStr = match[1];
      const inputMatch = testCaseStr.match(/\[(.*?)\]/);
      const targetMatch = testCaseStr.match(/, (\d+)\)/);

      if (inputMatch && targetMatch) {
        try {
          const input = inputMatch[1].split(',').map(Number);
          testCases.push({
            input,
            expectedOutput: expectedOutputs[index] || [],
          });
          index++;
        } catch (error) {
          console.error('Error parsing Swift test case:', error);
        }
      }
    }
    return testCases;
  },

  parseDartTestCases(matches: IterableIterator<RegExpMatchArray>): TestCase[] {
    const expectedOutputs = [[0, 1], [1, 2]];
    let index = 0;
    const testCases: TestCase[] = [];

    for (const match of matches) {
      try {
        const testCaseStr = match[1];
        const inputMatch = testCaseStr.match(/'input':\s*\[(.*?)\]/);
        const targetMatch = testCaseStr.match(/'target':\s*(\d+)/);

        if (inputMatch && targetMatch) {
          const input = inputMatch[1].split(',').map(s => parseInt(s.trim()));
          testCases.push({
            input,
            expectedOutput: expectedOutputs[index] || [],
          });
          index++;
          console.log('Added Dart test case:', { input, expectedOutput: expectedOutputs[index - 1] });
        }
      } catch (error) {
        console.error('Error parsing Dart test case:', error);
      }
    }
    return testCases;
  },

  getTemplateLines(language: string): number {
    switch (language) {
      case 'javascript':
        return 14;
      case 'python':
        return 12;
      case 'java':
        return 16;
      case 'cpp':
        return 19;
      case 'c':
        return 25;
      case 'php':
        return 15;
      case 'kotlin':
        return 13;
      case 'swift':
        return 14;
      case 'dart':
        return 14;
      default:
        return 0;
    }
  },

  extractTestCases(driverCode: string, language: string): TestCase[] {
    try {
      const testCases: TestCase[] = [];

      // Define expected outputs for the Two Sum problem
      // These are the expected indices for each test case
      const expectedOutputs = [
        [0, 1],  // For input [2, 7, 11, 15], target 9
        [1, 2]   // For input [3, 2, 4], target 6
      ];

      switch (language) {
        case 'javascript':
          // Extract test cases from JavaScript driver code
          const jsTestCasesMatch = driverCode.match(/const testCases = \[([\s\S]*?)\];/);
          if (jsTestCasesMatch) {
            const testCasesStr = jsTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);

            let index = 0;
            for (const match of testCaseMatches) {
              const testCaseStr = match[1];
              const inputMatch = testCaseStr.match(/input: (\[.*?\]),/);
              const targetMatch = testCaseStr.match(/target: (\d+)/);
              if (inputMatch && targetMatch) {
                try {
                  const input = JSON.parse(inputMatch[1]);
                  const expectedOutput = expectedOutputs[index] || [];

                  testCases.push({
                    input,
                    expectedOutput,
                  });

                  index++;
                } catch (error) {
                  console.error('Error parsing JavaScript test case:', error);
                }
              }
            }
          }
          break;

        case 'python':
          // Extract test cases from Python driver code
          const pyTestCasesMatch = driverCode.match(/test_cases\s*=\s*\[([\s\S]*?)\]/);
          if (pyTestCasesMatch) {
            const testCasesStr = pyTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);

            let index = 0;
            for (const match of testCaseMatches) {
              const testCaseStr = match[1];
              const inputMatch = testCaseStr.match(/"input":\s*(\[.*?\])/);
              const targetMatch = testCaseStr.match(/"target":\s*(\d+)/);
              if (inputMatch && targetMatch) {
                try {
                  const input = JSON.parse(inputMatch[1]);
                  const expectedOutput = expectedOutputs[index] || [];

                  testCases.push({
                    input,
                    expectedOutput,
                  });

                  index++;
                } catch (error) {
                  console.error('Error parsing Python test case:', error);
                }
              }
            }
          }
          break;

        case 'java':
          // Extract test cases from Java driver code
          const javaTestCasesMatch = driverCode.match(/int\[\]\[\]\s*testCases\s*=\s*\{([\s\S]*?)\};/);
          const javaTargetsMatch = driverCode.match(/int\[\]\s*targets\s*=\s*\{([\s\S]*?)\};/);
          if (javaTestCasesMatch && javaTargetsMatch) {
            const testCasesStr = javaTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);

            let index = 0;
            for (const match of testCaseMatches) {
              const testCaseStr = match[1];
              const numbers = testCaseStr.split(',').map(Number);
              const expectedOutput = expectedOutputs[index] || [];

              testCases.push({
                input: numbers,
                expectedOutput,
              });

              index++;
            }
          }
          break;

        case 'cpp':
          // Extract test cases from C++ driver code
          const cppTestCasesMatch = driverCode.match(/vector<vector<int>>\s*testCases\s*=\s*\{([\s\S]*?)\};/);
          const cppTargetsMatch = driverCode.match(/vector<int>\s*targets\s*=\s*\{([\s\S]*?)\};/);
          if (cppTestCasesMatch && cppTargetsMatch) {
            const testCasesStr = cppTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);

            let index = 0;
            for (const match of testCaseMatches) {
              const testCaseStr = match[1];
              const numbers = testCaseStr.split(',').map(Number);
              const expectedOutput = expectedOutputs[index] || [];

              testCases.push({
                input: numbers,
                expectedOutput,
              });

              index++;
            }
          }
          break;

        case 'c':
          // Extract test cases from C driver code
          const cTestCasesMatch = driverCode.match(/int testCases\[\]\[\d+\]\s*=\s*\{([\s\S]*?)\};/);
          const cTargetsMatch = driverCode.match(/int targets\[\]\s*=\s*\{([\s\S]*?)\};/);
          if (cTestCasesMatch && cTargetsMatch) {
            const testCasesStr = cTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\{([\s\S]*?)\}/g);

            let index = 0;
            for (const match of testCaseMatches) {
              const testCaseStr = match[1];
              const numbers = testCaseStr.split(',').map(Number);
              const expectedOutput = expectedOutputs[index] || [];

              testCases.push({
                input: numbers,
                expectedOutput,
              });

              index++;
            }
          }
          break;

        case 'php':
          // Extract test cases from PHP driver code
          const phpTestCasesMatch = driverCode.match(/\$testCases\s*=\s*\[([\s\S]*?)\];/);
          if (phpTestCasesMatch) {
            const testCasesStr = phpTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\["input"\s*=>\s*\[([\s\S]*?)\],\s*"target"\s*=>\s*(\d+)\]/g);

            let index = 0;
            for (const match of testCaseMatches) {
              try {
                // match[1] contains the input array
                const inputStr = match[1];

                // Parse the input array
                const input = inputStr.split(',').map(s => parseInt(s.trim()));
                const expectedOutput = expectedOutputs[index] || [];

                testCases.push({
                  input,
                  expectedOutput,
                });

                index++;
                console.log('Added PHP test case:', { input, expectedOutput });
              } catch (error) {
                console.error('Error parsing PHP test case:', error);
              }
            }
          }
          break;

        case 'kotlin':
          // Extract test cases from Kotlin driver code - using hardcoded test cases
          testCases.push(
            {
              input: [2, 7, 11, 15],
              expectedOutput: [0, 1]
            },
            {
              input: [3, 2, 4],
              expectedOutput: [1, 2]
            }
          );
          console.log('Using hardcoded Kotlin test cases:', testCases);
          break;

        case 'swift':
          // Extract test cases from Swift driver code
          const swiftTestCasesMatch = driverCode.match(/let testCases\s*=\s*\[([\s\S]*?)\]/);
          if (swiftTestCasesMatch) {
            const testCasesStr = swiftTestCasesMatch[1];
            const testCaseMatches = testCasesStr.matchAll(/\(\[([\s\S]*?)\], (\d+)\)/g);

            let index = 0;
            for (const match of testCaseMatches) {
              try {
                const numbersStr = match[1];
                const input = numbersStr.split(',').map(s => parseInt(s.trim()));
                const expectedOutput = expectedOutputs[index] || [];

                testCases.push({
                  input,
                  expectedOutput,
                });

                index++;
              } catch (error) {
                console.error('Error parsing Swift test case:', error);
              }
            }
          }
          break;

        case 'dart':
          // Extract test cases from Dart driver code - using hardcoded test cases
          testCases.push(
            {
              input: [2, 7, 11, 15],
              expectedOutput: [0, 1]
            },
            {
              input: [3, 2, 4],
              expectedOutput: [1, 2]
            }
          );
          console.log('Using hardcoded Dart test cases:', testCases);
          break;
      }

      return testCases;
    } catch (error) {
      console.error('Error extracting test cases:', error);
      return [];
    }
  },
};