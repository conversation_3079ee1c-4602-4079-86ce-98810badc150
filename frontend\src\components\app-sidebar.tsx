import * as React from 'react';
import {
  IconChartBar,
  IconCode,
  IconDashboard,
  IconDeviceDesktopAnalytics,
  IconHelp,
  IconInnerShadowTop,
  IconAward,
  IconListCheck,
  IconNotebook,
  IconSearch,
  IconSettings,
  IconTrophy,
  IconBrandGithub,
  IconSchool,
  IconCertificate,
} from '@tabler/icons-react';

import { NavDocuments } from '@/components/nav-documents';
import { NavMain } from '@/components/nav-main';
import { NavSecondary } from '@/components/nav-secondary';
import { NavUser } from '@/components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

const data = {
  user: {
    name: 'User',
    email: '<EMAIL>',
    avatar: '/avatars/default.jpg',
  },
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: IconDashboard,
    },
    {
      title: 'Practice',
      url: '/practice',
      icon: IconCode,
    },
    {
      title: 'Competitions',
      url: '/competitions',
      icon: IconTrophy,
    },
    {
      title: 'Leaderboard',
      url: '/leaderboard',
      icon: IconAward,
    },
    {
      title: 'My Submissions',
      url: '/submissions',
      icon: IconListCheck,
    },
  ],
  navClouds: [
    {
      title: 'Learning Paths',
      icon: IconSchool,
      isActive: true,
      url: '/learning',
      items: [
        {
          title: 'Algorithms',
          url: '/learning/algorithms',
        },
        {
          title: 'Data Structures',
          url: '/learning/data-structures',
        },
        {
          title: 'Problem Solving',
          url: '/learning/problem-solving',
        },
      ],
    },
    {
      title: 'Certificates',
      icon: IconCertificate,
      url: '/certificates',
      items: [
        {
          title: 'My Certificates',
          url: '/certificates/my',
        },
        {
          title: 'Available Exams',
          url: '/certificates/available',
        },
      ],
    },
    {
      title: 'Code Notebook',
      icon: IconNotebook,
      url: '/notebook',
      items: [
        {
          title: 'My Snippets',
          url: '/notebook/snippets',
        },
        {
          title: 'Templates',
          url: '/notebook/templates',
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: 'Settings',
      url: '/settings',
      icon: IconSettings,
    },
    {
      title: 'Help Center',
      url: '/help',
      icon: IconHelp,
    },
    {
      title: 'Search',
      url: '/search',
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: 'Performance Analytics',
      url: '/analytics',
      icon: IconDeviceDesktopAnalytics,
    },
    {
      name: 'Progress Stats',
      url: '/stats',
      icon: IconChartBar,
    },
    {
      name: 'GitHub Integration',
      url: '/integrations/github',
      icon: IconBrandGithub,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <a href="#">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">CodeTest Platform</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
