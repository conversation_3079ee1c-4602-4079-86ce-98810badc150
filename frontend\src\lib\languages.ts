export interface Language {
  id: string;
  name: string;
  extension: string;
  template: string;
  driverCode: string;
}

// Helper function to create driver code
const createDriverCode = (languageId: string): string => {
  switch (languageId) {
    case 'javascript':
    case 'typescript':
      return `// Test cases
const testCases = [
  { input: [2, 7, 11, 15], target: 9 },
  { input: [3, 2, 4], target: 6 }
];

// Run test cases
testCases.forEach((testCase, i) => {
  const result = solution(testCase.input, testCase.target);
  console.log(\`Test Case \${i + 1}:\`);
  console.log(\`Input: nums = \${JSON.stringify(testCase.input)}, target = \${testCase.target}\`);
  console.log(\`Output: \${JSON.stringify(result)}\`);
  console.log('---');
});`;
    case 'python':
      return `# Test cases
test_cases = [
    {"input": [2, 7, 11, 15], "target": 9},
    {"input": [3, 2, 4], "target": 6}
]

# Run test cases
for i, test_case in enumerate(test_cases, 1):
    result = solution(test_case["input"], test_case["target"])
    print(f"Test Case {i}:")
    print(f"Input: nums = {test_case['input']}, target = {test_case['target']}")
    print(f"Output: {result}")
    print("---")`;
    case 'java':
      return `
    // Main method to run test cases
    public static void main(String[] args) {
        // Test cases
        int[][] testCases = {
            {2, 7, 11, 15},
            {3, 2, 4}
        };
        int[] targets = {9, 6};

        // Run test cases
        for (int i = 0; i < testCases.length; i++) {
            int[] result = solution(testCases[i], targets[i]);
            System.out.println("Test Case " + (i + 1) + ":");
            System.out.println("Input: nums = " + java.util.Arrays.toString(testCases[i]) + ", target = " + targets[i]);
            System.out.println("Output: " + java.util.Arrays.toString(result));
            System.out.println("---");
        }
    }`;
    case 'cpp':
      return `#include <iostream>

void printVector(const vector<int>& vec) {
    cout << "[";
    for (int i = 0; i < vec.size(); i++) {
        cout << vec[i];
        if (i < vec.size() - 1) cout << ", ";
    }
    cout << "]";
}

int main() {
    // Test cases
    vector<vector<int>> testCases = {
        {2, 7, 11, 15},
        {3, 2, 4}
    };
    vector<int> targets = {9, 6};

    // Run test cases
    for (int i = 0; i < testCases.size(); i++) {
        vector<int> result = solution(testCases[i], targets[i]);
        cout << "Test Case " << (i + 1) << ":" << endl;
        cout << "Input: nums = ";
        printVector(testCases[i]);
        cout << ", target = " << targets[i] << endl;
        cout << "Output: ";
        printVector(result);
        cout << endl << "---" << endl;
    }
    return 0;
}`;
    case 'c':
      return `#include <stdio.h>

void printArray(int arr[], int size) {
    printf("[");
    for (int i = 0; i < size; i++) {
        printf("%d", arr[i]);
        if (i < size - 1) printf(", ");
    }
    printf("]");
}

int main() {
    // Test cases
    int testCases[][4] = {
        {2, 7, 11, 15},
        {3, 2, 4}
    };
    int targets[] = {9, 6};
    int sizes[] = {4, 3};

    // Run test cases
    for (int i = 0; i < 2; i++) {
        int result[2];
        int resultSize = 0;
        solution(testCases[i], sizes[i], targets[i], result, &resultSize);

        printf("Test Case %d:\\n", i + 1);
        printf("Input: nums = ");
        printArray(testCases[i], sizes[i]);
        printf(", target = %d\\n", targets[i]);
        printf("Output: ");
        printArray(result, resultSize);
        printf("\\n---\\n");
    }
    return 0;
}`;
    case 'php':
      return `
// Test cases
$testCases = [
    ["input" => [2, 7, 11, 15], "target" => 9],
    ["input" => [3, 2, 4], "target" => 6]
];

// Run test cases
foreach ($testCases as $index => $testCase) {
    $result = solution($testCase["input"], $testCase["target"]);

    // Use print instead of echo for more consistent output
    print("Test Case " . ($index + 1) . ":\\n");
    print("Input: nums = " . json_encode($testCase["input"]) . ", target = " . $testCase["target"] . "\\n");
    print("Output: " . json_encode($result) . "\\n");
    print("---\\n");
}`;
    case 'kotlin':
      return `
// Test cases
val testCases = listOf(
    Pair(intArrayOf(2, 7, 11, 15), 9),
    Pair(intArrayOf(3, 2, 4), 6)
)

// Override main function to run test cases
fun main() {
    // Run test cases
    testCases.forEachIndexed { i, pair ->
        val nums = pair.first
        val target = pair.second
        val result = solution(nums, target)
        println("Test Case \${i + 1}:")
        println("Input: nums = \${nums.joinToString(prefix = "[", postfix = "]")}, target = \${target}")
        println("Output: \${result.joinToString(prefix = "[", postfix = "]")}")
        println("---")
    }
}`;
    case 'swift':
      return `// Test cases
let testCases = [
    ([2, 7, 11, 15], 9),
    ([3, 2, 4], 6)
]

// Run test cases
for (i, testCase) in testCases.enumerated() {
    let (nums, target) = testCase
    let result = solution(nums, target)
    print("Test Case \\(i + 1):")
    print("Input: nums = \\(nums), target = \\(target)")
    print("Output: \\(result)")
    print("---")
}`;
    default:
      return '';
  }
};

export const languages: Language[] = [
  {
    id: 'javascript',
    name: 'JavaScript',
    extension: '.js',
    template: `// Write your solution here
function solution(nums, target) {
    // Your code here
    const map = {}; // to store value -> index

    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.hasOwnProperty(complement)) {
            return [map[complement], i];
        }
        map[nums[i]] = i;
    }
    return [];
}`,
    driverCode: createDriverCode('javascript'),
  },
  {
    id: 'typescript',
    name: 'TypeScript',
    extension: '.ts',
    template: `// Write your solution here
function solution(nums: number[], target: number): number[] {
    // Your code here
    const map: Record<number, number> = {}; // to store value -> index

    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.hasOwnProperty(complement)) {
            return [map[complement], i];
        }
        map[nums[i]] = i;
    }
    return [];
}`,
    driverCode: createDriverCode('typescript'),
  },
  {
    id: 'python',
    name: 'Python',
    extension: '.py',
    template: `# Write your solution here
def solution(nums, target):
    # Your code here
    num_map = {}  # value -> index

    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_map:
            return [num_map[complement], i]
        num_map[num] = i

    return []`,
    driverCode: createDriverCode('python'),
  },
  {
    id: 'java',
    name: 'Java',
    extension: '.java',
    template: `// Java solution for Two Sum problem
public class Main {
    // Solution method
    public static int[] solution(int[] nums, int target) {
        // Your code here
        java.util.Map<Integer, Integer> map = new java.util.HashMap<>();

        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            if (map.containsKey(complement)) {
                return new int[] { map.get(complement), i };
            }
            map.put(nums[i], i);
        }

        return new int[0];
    }

    // Main method with test cases
    public static void main(String[] args) {
        // Test cases
        int[][] testCases = {
            {2, 7, 11, 15},
            {3, 2, 4}
        };
        int[] targets = {9, 6};

        // Run test cases
        for (int i = 0; i < testCases.length; i++) {
            int[] result = solution(testCases[i], targets[i]);
            System.out.println("Test Case " + (i + 1) + ":");
            System.out.println("Input: nums = " + java.util.Arrays.toString(testCases[i]) + ", target = " + targets[i]);
            System.out.println("Output: " + java.util.Arrays.toString(result));
            System.out.println("---");
        }
    }
}`,
    driverCode: '', // No separate driver code needed, it's included in the template
  },
  {
    id: 'cpp',
    name: 'C++',
    extension: '.cpp',
    template: `#include <vector>
#include <unordered_map>
using namespace std;

// Write your solution here
vector<int> solution(vector<int>& nums, int target) {
    // Your code here
    unordered_map<int, int> map; // value -> index

    for (int i = 0; i < nums.size(); i++) {
        int complement = target - nums[i];
        if (map.find(complement) != map.end()) {
            return {map[complement], i};
        }
        map[nums[i]] = i;
    }

    return {};
}`,
    driverCode: createDriverCode('cpp'),
  },
  {
    id: 'c',
    name: 'C',
    extension: '.c',
    template: `#include <stdio.h>
#include <stdlib.h>

// Write your solution here
void solution(int* nums, int numsSize, int target, int* result, int* resultSize) {
    // Your code here
    int* map = (int*)malloc(numsSize * sizeof(int));
    int* indices = (int*)malloc(numsSize * sizeof(int));
    int mapSize = 0;

    for (int i = 0; i < numsSize; i++) {
        int complement = target - nums[i];
        for (int j = 0; j < mapSize; j++) {
            if (map[j] == complement) {
                result[0] = indices[j];
                result[1] = i;
                *resultSize = 2;
                free(map);
                free(indices);
                return;
            }
        }
        map[mapSize] = nums[i];
        indices[mapSize] = i;
        mapSize++;
    }
    *resultSize = 0;
    free(map);
    free(indices);
}`,
    driverCode: createDriverCode('c'),
  },
  {
    id: 'php',
    name: 'PHP',
    extension: '.php',
    template: `<?php
// Make sure errors are displayed
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Write your solution here
function solution($nums, $target) {
    // Your code here
    $map = array();

    for ($i = 0; $i < count($nums); $i++) {
        $complement = $target - $nums[$i];
        if (isset($map[$complement])) {
            return array($map[$complement], $i);
        }
        $map[$nums[$i]] = $i;
    }

    return array();
}`,
    driverCode: createDriverCode('php'),
  },
  {
    id: 'kotlin',
    name: 'Kotlin',
    extension: '.kt',
    template: `// Write your solution here
fun twoSum(nums: IntArray, target: Int): IntArray {
    // Your code here
    val map = mutableMapOf<Int, Int>()

    for (i in nums.indices) {
        val complement = target - nums[i]
        if (map.containsKey(complement)) {
            return intArrayOf(map[complement]!!, i)
        }
        map[nums[i]] = i
    }

    return intArrayOf()
}

// Main function with test cases
fun main() {
    // Test cases
    val testCase1 = intArrayOf(2, 7, 11, 15)
    val target1 = 9

    val testCase2 = intArrayOf(3, 2, 4)
    val target2 = 6

    // Run test cases
    println("Test Case 1:")
    println("Input: nums = [2, 7, 11, 15], target = 9")
    val result1 = twoSum(testCase1, target1)
    println("Output: [" + result1.joinToString(", ") + "]")
    println("---")

    println("Test Case 2:")
    println("Input: nums = [3, 2, 4], target = 6")
    val result2 = twoSum(testCase2, target2)
    println("Output: [" + result2.joinToString(", ") + "]")
    println("---")
}`,
    driverCode: ``,
  },
  {
    id: 'swift',
    name: 'Swift',
    extension: '.swift',
    template: `// Write your solution here
func solution(_ nums: [Int], _ target: Int) -> [Int] {
    // Your code here
    var map = [Int: Int]()

    for (i, num) in nums.enumerated() {
        let complement = target - num
        if let index = map[complement] {
            return [index, i]
        }
        map[num] = i
    }

    return []
}

// Test cases
let testCases = [
    ([2, 7, 11, 15], 9),
    ([3, 2, 4], 6)
]

// Run test cases
for (i, testCase) in testCases.enumerated() {
    let (nums, target) = testCase
    let _ = solution(nums, target)
    print("Test Case \\(i + 1):")
    print("Input: nums = \\(nums), target = \\(target)")
    print("Output: \\(solution(nums, target))")
    print("---")
}`,
    driverCode: ``,
  },
  {
    id: 'dart',
    name: 'Dart',
    extension: '.dart',
    template: `// Write your solution here
List<int> solution(List<int> nums, int target) {
  // Your code here
  final map = <int, int>{};

  for (var i = 0; i < nums.length; i++) {
    final complement = target - nums[i];
    if (map.containsKey(complement)) {
      return [map[complement]!, i];
    }
    map[nums[i]] = i;
  }

  return [];
}

void main() {
  // Test cases
  final testCases = [
    {'input': [2, 7, 11, 15], 'target': 9},
    {'input': [3, 2, 4], 'target': 6}
  ];

  // Run test cases
  for (var i = 0; i < testCases.length; i++) {
    final input = List<int>.from(testCases[i]['input']);
    final target = testCases[i]['target'];
    final result = solution(input, target);

    print('Test Case \${i + 1}:');
    print('Input: nums = \${input}, target = \${target}');
    print('Output: \${result}');
    print('---');
  }
}`,
    driverCode: ``,
  },
  {
    id: 'go',
    name: 'Go',
    extension: '.go',
    template: `package main

import (
	"fmt"
)

// Write your solution here
func solution(nums []int, target int) []int {
	// Your code here
	numMap := make(map[int]int)

	for i, num := range nums {
		complement := target - num
		if idx, found := numMap[complement]; found {
			return []int{idx, i}
		}
		numMap[num] = i
	}

	return []int{}
}

func main() {
	// Test cases
	testCases := []struct {
		nums   []int
		target int
	}{
		{[]int{2, 7, 11, 15}, 9},
		{[]int{3, 2, 4}, 6},
	}

	// Run test cases
	for i, tc := range testCases {
		result := solution(tc.nums, tc.target)
		fmt.Printf("Test Case %d:\\n", i+1)
		fmt.Printf("Input: nums = %v, target = %d\\n", tc.nums, tc.target)
		fmt.Printf("Output: %v\\n", result)
		fmt.Println("---")
	}
}`,
    driverCode: ``,
  },
  {
    id: 'ruby',
    name: 'Ruby',
    extension: '.rb',
    template: `# Write your solution here
def solution(nums, target)
  # Your code here
  map = {}

  nums.each_with_index do |num, i|
    complement = target - num
    if map.has_key?(complement)
      return [map[complement], i]
    end
    map[num] = i
  end

  return []
end

# Test cases
test_cases = [
  { input: [2, 7, 11, 15], target: 9 },
  { input: [3, 2, 4], target: 6 }
]

# Run test cases
test_cases.each_with_index do |test_case, index|
  result = solution(test_case[:input], test_case[:target])
  puts "Test Case #{index + 1}:"
  puts "Input: nums = #{test_case[:input]}, target = #{test_case[:target]}"
  puts "Output: #{result}"
  puts "---"
end`,
    driverCode: ``,
  },
  {
    id: 'scala',
    name: 'Scala',
    extension: '.scala',
    template: `// Write your solution here
object Solution {
  def solution(nums: Array[Int], target: Int): Array[Int] = {
    // Your code here
    val map = scala.collection.mutable.Map[Int, Int]()

    for (i <- nums.indices) {
      val complement = target - nums(i)
      if (map.contains(complement)) {
        return Array(map(complement), i)
      }
      map(nums(i)) = i
    }

    Array()
  }
}

object Main extends App {
  // Test cases
  val testCases = List(
    (Array(2, 7, 11, 15), 9),
    (Array(3, 2, 4), 6)
  )

  // Run test cases
  testCases.zipWithIndex.foreach { case ((nums, target), i) =>
    val result = Solution.solution(nums, target)
    println(s"Test Case \${i + 1}:")
    println(s"Input: nums = \${nums.mkString("[", ", ", "]")}, target = \$target")
    println(s"Output: \${result.mkString("[", ", ", "]")}")
    println("---")
  }
}`,
    driverCode: ``,
  },
  {
    id: 'rust',
    name: 'Rust',
    extension: '.rs',
    template: `use std::collections::HashMap;

// Write your solution here
fn solution(nums: Vec<i32>, target: i32) -> Vec<i32> {
    // Your code here
    let mut map = HashMap::new();

    for (i, &num) in nums.iter().enumerate() {
        let complement = target - num;
        if let Some(&index) = map.get(&complement) {
            return vec![index as i32, i as i32];
        }
        map.insert(num, i);
    }

    vec![]
}

fn main() {
    // Test cases
    let test_cases = vec![
        (vec![2, 7, 11, 15], 9),
        (vec![3, 2, 4], 6)
    ];

    // Run test cases
    for (i, (nums, target)) in test_cases.iter().enumerate() {
        let result = solution(nums.clone(), *target);
        println!("Test Case {}:", i + 1);
        println!("Input: nums = {:?}, target = {}", nums, target);
        println!("Output: {:?}", result);
        println!("---");
    }
}`,
    driverCode: ``,
  },
  {
    id: 'racket',
    name: 'Racket',
    extension: '.rkt',
    template: `#lang racket

;; Write your solution here
(define (solution nums target)
  ;; Your code here
  (define map (make-hash))

  (for/fold ([result '()])
            ([num nums]
             [i (in-naturals)])
    (if (not (null? result))
        result
        (let ([complement (- target num)])
          (if (hash-has-key? map complement)
              (list (hash-ref map complement) i)
              (begin
                (hash-set! map num i)
                '()))))))

;; Test cases
(define test-cases
  '(((2 7 11 15) 9)
    ((3 2 4) 6)))

;; Run test cases
(for ([test-case test-cases]
      [i (in-naturals 1)])
  (let* ([nums (first test-case)]
         [target (second test-case)]
         [result (solution nums target)])
    (printf "Test Case ~a:~n" i)
    (printf "Input: nums = ~a, target = ~a~n" nums target)
    (printf "Output: ~a~n" result)
    (printf "---~n")))`,
    driverCode: ``,
  },
];

export const getLanguageTemplate = (languageId: string): string => {
  const language = languages.find(lang => lang.id === languageId);
  if (!language) return languages[0].template;

  // If there's no driver code, just return the template
  if (!language.driverCode) {
    return language.template;
  }

  // Special handling for PHP to avoid duplicate <?php tags
  if (languageId === 'php') {
    return `${language.template}${language.driverCode}`;
  }

  // Special handling for languages with integrated driver code
  if (['kotlin', 'swift', 'dart', 'go', 'ruby', 'scala', 'rust', 'racket', 'typescript'].includes(languageId)) {
    return `${language.template}\n${language.driverCode}`;
  }

  return `${language.template}\n\n${language.driverCode}`;
};

// Language information for tooltips
interface LanguageInfo {
  compiler?: string;
  version?: string;
  standardLibraries?: string[];
  features?: string[];
  notes?: string[];
}

// Language information map
const languageInfoMap: Record<string, LanguageInfo> = {
  javascript: {
    compiler: 'Node.js',
    version: '16.14.0',
    standardLibraries: ['All standard Node.js libraries'],
    features: ['ES2020 features supported'],
    notes: ['Code is executed in a Node.js environment']
  },
  typescript: {
    compiler: 'TypeScript',
    version: '4.7.4 (compiled to Node.js)',
    standardLibraries: ['All standard TypeScript and Node.js libraries'],
    features: ['Strict type checking enabled'],
    notes: ['Code is transpiled to JavaScript and executed in Node.js']
  },
  python: {
    compiler: 'Python',
    version: '3.10.0',
    standardLibraries: ['All standard Python libraries'],
    notes: ['Code is executed in a Python interpreter']
  },
  java: {
    compiler: 'OpenJDK',
    version: '17.0.2',
    standardLibraries: ['java.util.*', 'java.io.*', 'java.math.*'],
    notes: ['Code is compiled with javac and executed on the JVM']
  },
  cpp: {
    compiler: 'g++',
    version: '11.2.0',
    standardLibraries: ['iostream', 'vector', 'string', 'map', 'unordered_map', 'algorithm'],
    features: ['C++17 standard'],
    notes: ['Compiled with -O2 optimization']
  },
  c: {
    compiler: 'gcc',
    version: '11.2.0',
    standardLibraries: ['stdio.h', 'stdlib.h', 'string.h', 'math.h'],
    features: ['C11 standard'],
    notes: ['Compiled with -O2 optimization']
  },
  php: {
    compiler: 'PHP',
    version: '8.1.0',
    standardLibraries: ['All standard PHP libraries'],
    notes: ['Code is executed in a PHP interpreter']
  },
  kotlin: {
    compiler: 'Kotlin',
    version: '1.7.0',
    standardLibraries: ['kotlin.collections.*', 'kotlin.io.*'],
    notes: ['Compiled to JVM bytecode and executed on the JVM']
  },
  swift: {
    compiler: 'Swift',
    version: '5.6.1',
    standardLibraries: ['Foundation', 'Swift standard library'],
    notes: ['Code is compiled and executed with the Swift compiler']
  },
  dart: {
    compiler: 'Dart',
    version: '2.17.0',
    standardLibraries: ['dart:core', 'dart:collection', 'dart:math'],
    notes: ['Code is executed in the Dart VM']
  },
  go: {
    compiler: 'Go',
    version: '1.18.0',
    standardLibraries: ['fmt', 'strings', 'math', 'sort'],
    notes: ['Code is compiled and executed with the Go compiler']
  },
  ruby: {
    compiler: 'Ruby',
    version: '3.1.0',
    standardLibraries: ['All standard Ruby libraries'],
    notes: ['Code is executed in a Ruby interpreter']
  },
  scala: {
    compiler: 'Scala',
    version: '3.1.0',
    standardLibraries: ['scala.collection.*', 'scala.io.*'],
    notes: ['Compiled to JVM bytecode and executed on the JVM']
  },
  rust: {
    compiler: 'Rust',
    version: '1.60.0',
    standardLibraries: ['std::collections', 'std::vec', 'std::string'],
    features: ['2021 edition'],
    notes: ['Compiled with -O optimization']
  },
  racket: {
    compiler: 'Racket',
    version: '8.5',
    standardLibraries: ['racket', 'racket/base', 'racket/list'],
    notes: ['Code is executed in a Racket interpreter']
  }
};

// Function to get language information
export const getLanguageInfo = (languageId: string): LanguageInfo => {
  return languageInfoMap[languageId] || {};
};