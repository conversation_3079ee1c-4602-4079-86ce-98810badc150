import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useStytch } from '@stytch/react';
import { useAuth } from '../context/AuthContext';

const Authenticate: React.FC = () => {
  const stytch = useStytch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();

  useEffect(() => {
    const authenticateUser = async () => {
      try {
        const token = searchParams.get('token');
        const type = searchParams.get('stytch_token_type');

        if (!token || !type) {
          throw new Error('Missing authentication parameters');
        }

        // Authenticate with Stytch
        const response = await stytch.oauth.authenticate({
          token,
          token_type: type as 'oauth' | 'oauth_google' | 'oauth_github',
        });

        if (response.user) {
          // Login with our backend
          await login(response.user.user_id);
          // Redirect to dashboard which will then route to the appropriate role-based dashboard
          navigate('/dashboard');
        }
      } catch (error) {
        console.error('Authentication error:', error);
        navigate('/auth?error=authentication_failed');
      }
    };

    authenticateUser();
  }, [stytch, searchParams, navigate, login]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900">Authenticating...</h2>
        <p className="mt-2 text-gray-600">Please wait while we complete your sign in</p>
      </div>
    </div>
  );
};

export default Authenticate; 