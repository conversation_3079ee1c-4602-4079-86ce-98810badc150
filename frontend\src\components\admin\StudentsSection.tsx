import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  IconSearch,
  IconPlus,
  IconDownload,
  IconFilter,
  IconEye,
  IconEdit,
  IconTrash,
} from '@tabler/icons-react';

export function StudentsSection() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock students data
  const students = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      joinDate: '2024-01-15',
      lastActive: '2 hours ago',
      problemsSolved: 45,
      score: 1250,
      status: 'active',
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      joinDate: '2024-01-20',
      lastActive: '1 day ago',
      problemsSolved: 32,
      score: 980,
      status: 'active',
    },
    {
      id: 3,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      joinDate: '2024-01-10',
      lastActive: '3 days ago',
      problemsSolved: 67,
      score: 1890,
      status: 'active',
    },
    {
      id: 4,
      name: 'Alice Brown',
      email: '<EMAIL>',
      joinDate: '2024-01-05',
      lastActive: '1 week ago',
      problemsSolved: 23,
      score: 650,
      status: 'inactive',
    },
    {
      id: 5,
      name: 'Charlie Wilson',
      email: '<EMAIL>',
      joinDate: '2024-01-25',
      lastActive: '5 minutes ago',
      problemsSolved: 89,
      score: 2340,
      status: 'active',
    },
  ];

  // Filter students based on search term and status
  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || student.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="px-4 lg:px-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">Students</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <IconDownload className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <IconPlus className="h-4 w-4 mr-2" />
            Add Student
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card className="p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search students by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <IconFilter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Students</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      {/* Students Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Join Date</TableHead>
              <TableHead>Last Active</TableHead>
              <TableHead>Problems Solved</TableHead>
              <TableHead>Score</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStudents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm || statusFilter !== 'all' 
                      ? 'No students match your search criteria.' 
                      : 'No students found.'}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-muted-foreground">{student.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>{student.joinDate}</TableCell>
                  <TableCell>{student.lastActive}</TableCell>
                  <TableCell>
                    <div className="font-medium">{student.problemsSolved}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{student.score.toLocaleString()}</div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(student.status)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="sm">
                        <IconEye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <IconEdit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                        <IconTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-2xl font-bold">{students.length}</div>
          <div className="text-sm text-muted-foreground">Total Students</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold">
            {students.filter(s => s.status === 'active').length}
          </div>
          <div className="text-sm text-muted-foreground">Active Students</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold">
            {Math.round(students.reduce((sum, s) => sum + s.problemsSolved, 0) / students.length)}
          </div>
          <div className="text-sm text-muted-foreground">Avg Problems Solved</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold">
            {Math.round(students.reduce((sum, s) => sum + s.score, 0) / students.length).toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Avg Score</div>
        </Card>
      </div>
    </div>
  );
}
