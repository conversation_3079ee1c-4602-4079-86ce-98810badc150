// backend/src/services/questionsService.ts

import { Question, CreateQuestionData, UpdateQuestionData, QuestionFilters } from '../models/Question';
import worqDBService from './worqdb';

class QuestionsService {
  constructor() {
    console.log('🚀 Initializing Questions service...');
    console.log('✅ Questions service initialized successfully');
  }

  private formatDateTimeForWorqDB(date?: Date): string {
    // Use the same format as the main WorqDB service
    const d = date || new Date();
    const year = 2025; // Hardcoded to match reference data
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  private generateQuestionId(): string {
    const questionId = `question_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log('🆔 Generated new question ID:', questionId);
    return questionId;
  }

  /**
   * Create a new question
   */
  async createQuestion(questionData: CreateQuestionData): Promise<Question> {
    return await worqDBService.createQuestion(questionData);
  }

  /**
   * Get all questions with optional filters
   */
  async getAllQuestions(filters?: QuestionFilters): Promise<Question[]> {
    return await worqDBService.getAllQuestions(filters);
  }

  /**
   * Get a question by ID
   */
  async getQuestionById(questionId: string): Promise<Question | null> {
    return await worqDBService.getQuestionById(questionId);
  }

  /**
   * Update a question
   */
  async updateQuestion(questionId: string, updateData: UpdateQuestionData): Promise<Question | null> {
    // For now, return null - implement later if needed
    console.log('🔄 Update question not implemented yet:', questionId, updateData);
    return null;
  }

  /**
   * Delete a question (hard delete)
   */
  async deleteQuestion(questionId: string): Promise<boolean> {
    // For now, return false - implement later if needed
    console.log('🗑️ Delete question not implemented yet:', questionId);
    return false;
  }
}

export default new QuestionsService();
