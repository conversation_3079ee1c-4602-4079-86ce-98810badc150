// backend/src/services/questionsService.ts

import { Question, CreateQuestionData, UpdateQuestionData, QuestionFilters } from '../models/Question';

interface WorqDBResponse {
  success: boolean;
  data?: any;
  rows?: number;
  message?: string;
}

class QuestionsService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    console.log('🚀 Initializing Questions service...');
    this.apiKey = process.env.WORQDB_API_KEY || 'wh_mar507qyDQlmk055CBABvnFyo2CsrdL2Ajj1entK1D';
    this.baseUrl = 'https://api.worqhat.com/api/db/run-query';
    console.log('✅ Questions service initialized successfully');
  }

  private async executeQuery(query: string, params?: Record<string, any>): Promise<WorqDBResponse> {
    try {
      console.log('🔍 Executing WorqDB query:', query);
      console.log('📝 Query params:', params);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          query,
          params
        })
      });

      const data = await response.json() as WorqDBResponse;

      if (!response.ok) {
        console.error('❌ WorqDB query failed:', data);
        throw new Error(data.message || 'Database query failed');
      }

      console.log('✅ WorqDB query successful:', { success: data.success, rows: data.rows });
      return data;
    } catch (error) {
      console.error('❌ Questions service error:', error);
      throw error;
    }
  }

  private formatDateTimeForWorqDB(date?: Date): string {
    const d = date || new Date();
    return d.toISOString().slice(0, 19).replace('T', ' ');
  }

  private generateQuestionId(): string {
    const questionId = `question_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log('🆔 Generated new question ID:', questionId);
    return questionId;
  }

  /**
   * Create a new question
   */
  async createQuestion(questionData: CreateQuestionData): Promise<Question> {
    try {
      console.log('📝 Creating new question:', questionData.title);

      const questionId = this.generateQuestionId();
      const formattedDate = this.formatDateTimeForWorqDB();

      // Prepare constraints and test_cases as JSON strings if they exist
      const constraints = questionData.constraints ? JSON.stringify(questionData.constraints) : null;
      const testCases = questionData.test_cases ? JSON.stringify(questionData.test_cases) : null;

      const query = `
        INSERT INTO questions (
          documentId, createdAt, updatedAt, title, description, difficulty, 
          category, constraints, test_cases, created_by, is_active
        ) VALUES (
          {documentId}, {createdAt}, {updatedAt}, {title}, {description}, {difficulty},
          {category}, {constraints}, {test_cases}, {created_by}, {is_active}
        )
      `;

      const params = {
        documentId: questionId,
        createdAt: formattedDate,
        updatedAt: formattedDate,
        title: questionData.title,
        description: questionData.description,
        difficulty: questionData.difficulty,
        category: questionData.category,
        constraints: constraints,
        test_cases: testCases,
        created_by: questionData.created_by || 'admin',
        is_active: 1
      };

      await this.executeQuery(query, params);

      console.log('✅ Question created successfully:', questionId);

      // Return the created question
      return {
        documentId: questionId,
        createdAt: formattedDate,
        updatedAt: formattedDate,
        title: questionData.title,
        description: questionData.description,
        difficulty: questionData.difficulty,
        category: questionData.category,
        constraints: questionData.constraints,
        test_cases: questionData.test_cases,
        created_by: questionData.created_by || 'admin',
        is_active: true
      };
    } catch (error) {
      console.error('❌ Error creating question:', error);
      throw error;
    }
  }

  /**
   * Get all questions with optional filters
   */
  async getAllQuestions(filters?: QuestionFilters): Promise<Question[]> {
    try {
      console.log('🔍 Fetching all questions with filters:', filters);

      let query = `
        SELECT documentId, createdAt, updatedAt, title, description, difficulty,
               category, constraints, test_cases, created_by, is_active
        FROM questions
        WHERE is_active = 1
      `;

      const params: Record<string, any> = {};

      // Add filters to query
      if (filters?.difficulty) {
        query += ' AND difficulty = {difficulty}';
        params.difficulty = filters.difficulty;
      }

      if (filters?.category) {
        query += ' AND category = {category}';
        params.category = filters.category;
      }

      if (filters?.created_by) {
        query += ' AND created_by = {created_by}';
        params.created_by = filters.created_by;
      }

      query += ' ORDER BY createdAt DESC';

      const result = await this.executeQuery(query, Object.keys(params).length > 0 ? params : undefined);

      if (!result.data || !Array.isArray(result.data)) {
        console.log('📝 No questions found');
        return [];
      }

      console.log('✅ Questions fetched successfully:', result.data.length);
      return result.data;
    } catch (error) {
      console.error('❌ Error fetching questions:', error);
      throw error;
    }
  }

  /**
   * Get a question by ID
   */
  async getQuestionById(questionId: string): Promise<Question | null> {
    try {
      console.log('🔍 Fetching question by ID:', questionId);

      const query = `
        SELECT documentId, createdAt, updatedAt, title, description, difficulty,
               category, constraints, test_cases, created_by, is_active
        FROM questions
        WHERE documentId = {questionId} AND is_active = 1
        LIMIT 1
      `;

      const result = await this.executeQuery(query, { questionId });

      if (!result.data || !Array.isArray(result.data) || result.data.length === 0) {
        console.log('❌ Question not found:', questionId);
        return null;
      }

      console.log('✅ Question found:', questionId);
      return result.data[0];
    } catch (error) {
      console.error('❌ Error fetching question:', error);
      throw error;
    }
  }

  /**
   * Update a question
   */
  async updateQuestion(questionId: string, updateData: UpdateQuestionData): Promise<Question | null> {
    try {
      console.log('🔄 Updating question:', questionId);

      const formattedDate = this.formatDateTimeForWorqDB();
      const updateFields: string[] = [];
      const params: Record<string, any> = {
        questionId,
        updatedAt: formattedDate
      };

      // Build dynamic update query
      if (updateData.title !== undefined) {
        updateFields.push('title = {title}');
        params.title = updateData.title;
      }

      if (updateData.description !== undefined) {
        updateFields.push('description = {description}');
        params.description = updateData.description;
      }

      if (updateData.difficulty !== undefined) {
        updateFields.push('difficulty = {difficulty}');
        params.difficulty = updateData.difficulty;
      }

      if (updateData.category !== undefined) {
        updateFields.push('category = {category}');
        params.category = updateData.category;
      }

      if (updateData.constraints !== undefined) {
        updateFields.push('constraints = {constraints}');
        params.constraints = updateData.constraints ? JSON.stringify(updateData.constraints) : null;
      }

      if (updateData.test_cases !== undefined) {
        updateFields.push('test_cases = {test_cases}');
        params.test_cases = updateData.test_cases ? JSON.stringify(updateData.test_cases) : null;
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateFields.push('updatedAt = {updatedAt}');

      const query = `
        UPDATE questions
        SET ${updateFields.join(', ')}
        WHERE documentId = {questionId} AND is_active = 1
      `;

      await this.executeQuery(query, params);

      console.log('✅ Question updated successfully:', questionId);

      // Return the updated question
      return await this.getQuestionById(questionId);
    } catch (error) {
      console.error('❌ Error updating question:', error);
      throw error;
    }
  }

  /**
   * Delete a question (soft delete)
   */
  async deleteQuestion(questionId: string): Promise<boolean> {
    try {
      console.log('🗑️ Deleting question:', questionId);

      const formattedDate = this.formatDateTimeForWorqDB();

      const query = `
        UPDATE questions
        SET is_active = 0, updatedAt = {updatedAt}
        WHERE documentId = {questionId}
      `;

      await this.executeQuery(query, {
        questionId,
        updatedAt: formattedDate
      });

      console.log('✅ Question deleted successfully:', questionId);
      return true;
    } catch (error) {
      console.error('❌ Error deleting question:', error);
      throw error;
    }
  }
}

export default new QuestionsService();
