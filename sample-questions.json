[{"title": "Two Sum", "description": "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\n\nYou may assume that each input would have exactly one solution, and you may not use the same element twice.\n\nYou can return the answer in any order.", "difficulty": "Easy", "category": "Array", "examples": [{"input": "nums = [2,7,11,15], target = 9", "output": "[0,1]", "explanation": "Because nums[0] + nums[1] == 9, we return [0, 1]."}, {"input": "nums = [3,2,4], target = 6", "output": "[1,2]", "explanation": "Because nums[1] + nums[2] == 6, we return [1, 2]."}, {"input": "nums = [3,3], target = 6", "output": "[0,1]", "explanation": "Because nums[0] + nums[1] == 6, we return [0, 1]."}], "constraints": ["2 <= nums.length <= 10^4", "-10^9 <= nums[i] <= 10^9", "-10^9 <= target <= 10^9", "Only one valid answer exists."], "test_cases": [{"input": {"nums": [2, 7, 11, 15], "target": 9}, "expectedOutput": [0, 1], "description": "Basic test case with answer at beginning"}, {"input": {"nums": [3, 2, 4], "target": 6}, "expectedOutput": [1, 2], "description": "Test case with answer in middle"}, {"input": {"nums": [3, 3], "target": 6}, "expectedOutput": [0, 1], "description": "Test case with duplicate numbers"}, {"input": {"nums": [1, 2, 3, 4, 5], "target": 9}, "expectedOutput": [3, 4], "description": "Test case with larger array"}]}, {"title": "Valid Parentheses", "description": "Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.\n\nAn input string is valid if:\n1. Open brackets must be closed by the same type of brackets.\n2. Open brackets must be closed in the correct order.\n3. Every close bracket has a corresponding open bracket of the same type.", "difficulty": "Easy", "category": "<PERSON><PERSON>", "examples": [{"input": "s = \"()\"", "output": "true", "explanation": "The string contains valid parentheses."}, {"input": "s = \"()[]{}\"", "output": "true", "explanation": "All brackets are properly matched."}, {"input": "s = \"(]\"", "output": "false", "explanation": "Brackets are not properly matched."}], "constraints": ["1 <= s.length <= 10^4", "s consists of parentheses only '()[]{}'."], "test_cases": [{"input": {"s": "()"}, "expectedOutput": true, "description": "Simple valid parentheses"}, {"input": {"s": "()[]{}"}, "expectedOutput": true, "description": "Multiple valid bracket types"}, {"input": {"s": "(]"}, "expectedOutput": false, "description": "Mismatched brackets"}, {"input": {"s": "([)]"}, "expectedOutput": false, "description": "Incorrectly nested brackets"}, {"input": {"s": "{[]}"}, "expectedOutput": true, "description": "Properly nested brackets"}]}, {"title": "Merge Two Sorted Lists", "description": "You are given the heads of two sorted linked lists list1 and list2.\n\nMerge the two lists into one sorted list. The list should be made by splicing together the nodes of the first two lists.\n\nReturn the head of the merged linked list.", "difficulty": "Easy", "category": "Linked List", "examples": [{"input": "list1 = [1,2,4], list2 = [1,3,4]", "output": "[1,1,2,3,4,4]", "explanation": "The merged list is [1,1,2,3,4,4]."}, {"input": "list1 = [], list2 = []", "output": "[]", "explanation": "Both lists are empty."}, {"input": "list1 = [], list2 = [0]", "output": "[0]", "explanation": "One list is empty, return the other."}], "constraints": ["The number of nodes in both lists is in the range [0, 50].", "-100 <= Node.val <= 100", "Both list1 and list2 are sorted in non-decreasing order."], "test_cases": [{"input": {"list1": [1, 2, 4], "list2": [1, 3, 4]}, "expectedOutput": [1, 1, 2, 3, 4, 4], "description": "Basic merge of two sorted lists"}, {"input": {"list1": [], "list2": []}, "expectedOutput": [], "description": "Both lists empty"}, {"input": {"list1": [], "list2": [0]}, "expectedOutput": [0], "description": "One list empty"}, {"input": {"list1": [1, 2, 3], "list2": [4, 5, 6]}, "expectedOutput": [1, 2, 3, 4, 5, 6], "description": "No overlapping values"}]}]