import { useEffect, useState } from 'react';
import { useOrg } from '@/context/OrgContext';
import { useStytch, useStytchSession, useStytchUser } from '@stytch/react';
import { motion } from 'framer-motion';
import { Code, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

export default function AuthenticatePage() {
  const navigate = useNavigate();
  const { orgName, orgLogo, orgIcon } = useOrg();
  const stytch = useStytch();
  const { session } = useStytchSession();
  const { user: stytchUser } = useStytchUser();
  const { user, isAuthenticated, isLoading } = useAuth();
  const [authStatus, setAuthStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Authenticating...');

  useEffect(() => {
    const authenticateUser = async () => {
      console.log('Authenticating...', session);
      try {
        if (session) {
          setAuthStatus('success');
          setMessage('Authentication successful!');

          // Check if user needs to complete onboarding
          if (isAuthenticated && user) {
            // Check if onboarding is complete
            let onboardingComplete = user.onboardingComplete || false;

            // Try to parse metadata if it exists
            if (user.metadata) {
              try {
                const metadata = typeof user.metadata === 'string'
                  ? JSON.parse(user.metadata)
                  : user.metadata;

                if (metadata && metadata.onboardingComplete === true) {
                  onboardingComplete = true;
                }
              } catch (error) {
                console.error('Error parsing metadata in authenticate:', error);
              }
            }

            // Redirect based on onboarding status and role
            setTimeout(() => {
              if (!onboardingComplete) {
                console.log('User needs to complete onboarding, redirecting from authenticate');
                navigate('/onboarding');
              } else {
                // Check user role for redirection
                const userRole = user.role || 'student'; // Default to student if no role

                if (userRole === 'admin') {
                  console.log('Admin user detected, redirecting to admin dashboard');
                  navigate('/admin');
                } else {
                  console.log('Student user detected, redirecting to dashboard');
                  navigate('/dashboard');
                }
              }
            }, 1500);
          } else if (!isLoading) {
            // If auth is done loading but user is not authenticated, redirect to dashboard
            // (App.tsx will handle redirecting to onboarding if needed)
            setTimeout(() => {
              navigate('/dashboard');
            }, 1500);
          }
        } else {
          const token = new URLSearchParams(window.location.search).get('token');
          if (token) {
            await stytch.oauth.authenticate(token, {
              session_duration_minutes: 100000,
            });
            setAuthStatus('success');
            setMessage('Authentication successful!');

            // Wait for auth state to update before redirecting
            setTimeout(() => {
              navigate('/dashboard');
            }, 2000);
          } else {
            setAuthStatus('error');
            setMessage('Authentication failed. No token found.');
          }
        }
      } catch (error) {
        console.error('Authentication error:', error);
        setAuthStatus('error');
        setMessage('Authentication failed. Please try again.');
      }
    };

    authenticateUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stytch, session, user, isAuthenticated, isLoading]);

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-8 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col items-center gap-8">
        {/* Organization Logo */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          {orgLogo ? (
            <img src={orgLogo} alt={orgName} className="h-16 mx-auto" />
          ) : (
            <div className="flex items-center gap-2 font-medium">
              {orgIcon}
              <span className="text-xl">{orgName}</span>
            </div>
          )}
        </motion.div>

        {/* Loading Animation */}
        <motion.div
          className="flex flex-col items-center justify-center gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          {authStatus === 'loading' && (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ repeat: Infinity, duration: 2, ease: 'linear' }}
              className="relative"
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <Code className="h-8 w-8 text-primary" />
              </div>
              <Loader2 className="h-16 w-16 text-muted-foreground opacity-50" />
            </motion.div>
          )}

          {authStatus === 'success' && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', stiffness: 200, damping: 10 }}
              className="bg-green-100 dark:bg-green-900 rounded-full p-4"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-10 w-10 text-green-600 dark:text-green-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <motion.path
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </motion.div>
          )}

          {authStatus === 'error' && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', stiffness: 200, damping: 10 }}
              className="bg-red-100 dark:bg-red-900 rounded-full p-4"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-10 w-10 text-red-600 dark:text-red-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <motion.path
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="text-center"
          >
            <h2 className="text-xl font-semibold mb-2">{message}</h2>
            {authStatus === 'error' && (
              <p className="text-sm text-muted-foreground">
                <a href="/auth" className="underline hover:text-primary">
                  Return to login page
                </a>
              </p>
            )}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
