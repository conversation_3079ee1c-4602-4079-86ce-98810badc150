@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* LeetCode-style UI customizations */
.readOnlyRegion {
  opacity: 0.7;
  background-color: rgba(0, 0, 0, 0.03);
}

.dark .readOnlyRegion {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Collapsible test case styling */
.chevron-down {
  transition: transform 0.2s ease;
}

[id^="test-details-"]:not(.hidden) + div .chevron-down {
  transform: rotate(180deg);
}

/* LeetCode-style tabs */
[data-slot="tabs-trigger"] {
  font-weight: 500;
}

[data-slot="tabs-trigger"][data-state="active"] {
  color: var(--primary);
  font-weight: 600;
}

/* Improve code editor appearance */
.monaco-editor .margin {
  background-color: transparent !important;
}

/* Improve test case display */
.test-case-header {
  cursor: pointer;
  user-select: none;
}

/* LeetCode-style buttons */
button[data-slot="button"] {
  font-weight: 500;
}

/* Language info tooltip styling */
[data-radix-popper-content-wrapper] {
  max-height: 300px;
  overflow-y: auto;
}

[data-slot="tooltip-content"] {
  max-width: 300px;
  max-height: 250px;
  overflow-y: auto;
  scrollbar-width: thin;
  background-color: #1e1e1e !important;
  color: #e0e0e0 !important;
  border-color: #333 !important;
}

[data-slot="tooltip-content"] h4 {
  color: #fff !important;
}

[data-slot="tooltip-content"] .text-muted-foreground {
  color: #aaa !important;
}

[data-slot="tooltip-content"]::-webkit-scrollbar {
  width: 6px;
}

[data-slot="tooltip-content"]::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

[data-slot="tooltip-content"]::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* Fix dropdown scrolling */
.scrollable-dropdown {
  max-height: 300px !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
}

/* Ensure select content is scrollable */
[data-radix-select-content] {
  max-height: 300px !important;
  overflow-y: auto !important;
}

/* Improve spacing in dropdown items */
.language-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding-right: 8px !important;
}

/* Space between language name and info icon */
.language-item > div {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  justify-content: space-between !important;
}

/* Fix select item styling */
[data-radix-select-item] {
  padding: 6px 8px !important;
}

/* Fix select value display */
[data-radix-select-value] {
  flex: 1 !important;
}

/* Fix tooltip positioning */
[data-radix-tooltip-trigger] {
  display: inline-flex !important;
  cursor: help !important;
}

/* Ensure tooltip content is dark */
[data-radix-tooltip-content] {
  background-color: #1e1e1e !important;
  color: #e0e0e0 !important;
  border-color: #333 !important;
  max-height: 250px !important;
  overflow-y: auto !important;
}

/* Fix dropdown scrollbar */
[data-radix-select-content]::-webkit-scrollbar {
  width: 6px;
}

[data-radix-select-content]::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

[data-radix-select-content]::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* Timer styling */
.timer-button {
  display: flex;
  align-items: center;
  background-color: #1e1e1e;
  color: #4ade80;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 6px 12px;
  font-family: monospace;
  transition: all 0.2s ease;
}

.timer-button:hover {
  background-color: #2d2d2d;
  border-color: #4ade80;
}

.timer-button.timer-running {
  background-color: #4ade80;
  color: #1e1e1e;
  border-color: #4ade80;
}

.timer-button.timer-running:hover {
  background-color: #22c55e;
}

.timer-reset-button {
  background-color: #1e1e1e;
  color: #f97316;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 6px;
  transition: all 0.2s ease;
}

.timer-reset-button:hover {
  background-color: #2d2d2d;
  border-color: #f97316;
  color: #fb923c;
}

.timer-display {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Dark mode adjustments */
.dark .timer-button {
  background-color: #1e1e1e;
  color: #4ade80;
  border-color: #333;
}

.dark .timer-button:hover {
  background-color: #2d2d2d;
  border-color: #4ade80;
}

.dark .timer-button.timer-running {
  background-color: #4ade80;
  color: #1e1e1e;
  border-color: #4ade80;
}

.dark .timer-button.timer-running:hover {
  background-color: #22c55e;
}

.dark .timer-reset-button {
  background-color: #1e1e1e;
  color: #f97316;
  border-color: #333;
}

.dark .timer-reset-button:hover {
  background-color: #2d2d2d;
  border-color: #f97316;
  color: #fb923c;
}

/* Fullscreen coding environment styles */
:fullscreen {
  background-color: var(--background);
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

:-webkit-full-screen {
  background-color: var(--background);
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

:-moz-full-screen {
  background-color: var(--background);
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

:-ms-fullscreen {
  background-color: var(--background);
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Ensure editor takes full height in fullscreen mode */
:fullscreen .monaco-editor,
:-webkit-full-screen .monaco-editor,
:-moz-full-screen .monaco-editor,
:-ms-fullscreen .monaco-editor {
  height: 100% !important;
}

/* Ensure the coding interface expands properly in fullscreen */
:fullscreen .flex-1,
:-webkit-full-screen .flex-1,
:-moz-full-screen .flex-1,
:-ms-fullscreen .flex-1 {
  height: calc(100vh - 3.5rem) !important;
}
