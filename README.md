# Code Test Platform

## Overview

The Code Test Platform is an advanced, admin-controlled system designed to facilitate coding assessments with integrated AI assistance and guided helpers. This platform enables organizations to create, administer, and evaluate coding tests while providing test-takers with intelligent support throughout the process.

## Purpose

This platform addresses common challenges in technical assessments by:

- Providing a controlled environment for coding tests
- Offering AI-powered assistance to guide test-takers
- Enabling administrators to customize test parameters and evaluation criteria
- Creating a fair and supportive testing experience that better reflects real-world development scenarios

## Key Features

### For Administrators

- **Test Creation and Management**: Create, edit, and manage coding tests with customizable parameters
- **Question Library**: Build and maintain a repository of coding challenges across different difficulty levels
- **Candidate Management**: Track candidate progress and performance
- **Customizable AI Assistance Levels**: Configure how much AI help is available to candidates
- **Detailed Analytics**: Gain insights into test performance, common challenges, and candidate skills

### For Test-Takers

- **AI-Assisted Coding Environment**: Get intelligent suggestions and guidance while solving problems
- **Step-by-Step Helpers**: Access guided walkthroughs for complex challenges
- **Real-time Feedback**: Receive immediate feedback on code quality, efficiency, and correctness
- **Documentation Access**: Reference relevant documentation and resources within the platform

## Technology Stack

- **Frontend**: React with TypeScript, built using Vite
- **Backend**: Node.js with Express and TypeScript
- **Database**: WorqDB for all data storage needs
- **AI Integration**: WorqHat AI services for code assistance and evaluation
- **Authentication**: Secure user authentication and role-based access control

## Project Structure

The project is organized into two main directories:

- **`/frontend`**: Contains the React-based user interface

  - See [Frontend README](./frontend/README.md) for detailed information

- **`/backend`**: Contains the Express API server
  - See [Backend README](./backend/README.md) for detailed information

## Development Philosophy

1. **WorqHat First**: Leverage WorqHat workflows and services whenever possible
2. **AI-Augmented Experience**: Focus on creating an environment where AI enhances human capabilities
3. **Fairness and Accessibility**: Ensure the platform provides equal opportunities for all test-takers
4. **Real-world Relevance**: Design tests that reflect actual development scenarios

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- WorqHat account with API access

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd code-test-platform
```

2. Install dependencies for both frontend and backend

```bash
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

3. Configure environment variables

```bash
# In the backend directory
cp .env.example .env
# Edit .env with your WorqHat API keys and other configuration
```

4. Start the development servers

```bash
# Start backend server (from backend directory)
npm run dev

# Start frontend server (from frontend directory)
cd ../frontend
npm run dev
```

## Deployment

The platform can be deployed using various cloud services. Detailed deployment instructions can be found in the deployment guide (coming soon).

## Contributing

Contributions are welcome! Please read the contribution guidelines before submitting pull requests.

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Submit a pull request

## License

[License information to be added]

## Contact

[Contact information to be added]
