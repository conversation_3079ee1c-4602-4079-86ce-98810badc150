import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { IconSearch, IconBell, IconLogout, IconPlus } from '@tabler/icons-react';
import { Input } from '@/components/ui/input';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

export function AdminSiteHeader() {
  const navigate = useNavigate();
  const { logout } = useAuth();

  // Handle logout with proper navigation
  const handleLogout = async () => {
    console.log('🚪 Admin initiated logout from AdminSiteHeader');

    try {
      await logout();
      console.log('✅ Admin logout successful, redirecting to login page');
    } catch (error) {
      console.error('❌ Error during admin logout:', error);
    } finally {
      // Always navigate to auth page, regardless of logout success
      navigate('/auth');
    }
  };

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6 my-3">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4" />

        {/* Search Bar */}
        <div className="ml-6 hidden md:flex relative max-w-md flex-1">
          <IconSearch className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search questions, students, reports..."
            className="pl-8 w-full bg-background"
          />
        </div>

        {/* Header Actions */}
        <div className="ml-auto flex items-center gap-4">
          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <IconBell className="h-5 w-5" />
            {/* Notification indicator */}
            <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-red-500"></span>
          </Button>

          {/* Quick Add Button */}
          <Button
            variant="default"
            size="sm"
            className="hidden sm:flex"
            onClick={() => {
              // This could open a quick add modal or navigate to add question page
              console.log('Quick add clicked');
            }}
          >
            <IconPlus className="mr-2 h-4 w-4" />
            Quick Add
          </Button>

          {/* Logout Button */}
          <Button
            variant="ghost"
            size="sm"
            className="ml-2"
            onClick={handleLogout}
          >
            <IconLogout className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>
      </div>
    </header>
  );
}
