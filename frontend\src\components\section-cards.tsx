import { IconTrendingUp, IconCode, IconTrophy, IconUsers, IconStar } from '@tabler/icons-react';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function SectionCards() {
  return (
    <>
      <div className="flex justify-between items-center px-4 lg:px-6 mb-4">
        <h2 className="text-2xl font-semibold">Your Dashboard</h2>
      </div>

      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Solved Problems</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              127
            </CardTitle>
            <CardAction>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
              >
                <IconTrendingUp className="text-green-600 dark:text-green-400" />
                +15
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              15 problems solved this week <IconTrendingUp className="size-4 text-green-600" />
            </div>
            <div className="text-muted-foreground">You're in the top 15% of users</div>
          </CardFooter>
        </Card>

        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Current Rank</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              1,234
            </CardTitle>
            <CardAction>
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
              >
                <IconUsers className="text-blue-600 dark:text-blue-400" />
                Global
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              Improved by 56 positions <IconTrendingUp className="size-4 text-green-600" />
            </div>
            <div className="text-muted-foreground">Keep solving to climb the leaderboard</div>
          </CardFooter>
        </Card>

        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Active Competitions</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              3
            </CardTitle>
            <CardAction>
              <Badge
                variant="outline"
                className="bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400"
              >
                <IconTrophy className="text-purple-600 dark:text-purple-400" />
                New
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              Weekly Challenge ends in 2 days <IconCode className="size-4" />
            </div>
            <div className="text-muted-foreground">1 competition you haven't joined yet</div>
          </CardFooter>
        </Card>

        <Card className="@container/card">
          <CardHeader>
            <CardDescription>Your Rating</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              1856
            </CardTitle>
            <CardAction>
              <Badge
                variant="outline"
                className="bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400"
              >
                <IconStar className="text-yellow-600 dark:text-yellow-400" />
                Expert
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              +45 points this month <IconTrendingUp className="size-4 text-green-600" />
            </div>
            <div className="text-muted-foreground">144 points to reach Master level</div>
          </CardFooter>
        </Card>
      </div>

      <div className="mt-8 px-4 lg:px-6">
        <h3 className="text-xl font-medium mb-4">Recommended Challenges</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Two Sum</CardTitle>
                  <CardDescription>Array, Hash Table</CardDescription>
                </div>
                <Badge>Easy</Badge>
              </div>
            </CardHeader>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Success Rate: 72%</div>
              <Badge variant="outline" className="cursor-pointer">
                Solve Challenge
              </Badge>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Valid Parentheses</CardTitle>
                  <CardDescription>Stack, String</CardDescription>
                </div>
                <Badge>Medium</Badge>
              </div>
            </CardHeader>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Success Rate: 54%</div>
              <Badge variant="outline" className="cursor-pointer">
                Solve Challenge
              </Badge>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Merge K Sorted Lists</CardTitle>
                  <CardDescription>Linked List, Divide & Conquer</CardDescription>
                </div>
                <Badge>Hard</Badge>
              </div>
            </CardHeader>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Success Rate: 36%</div>
              <Badge variant="outline" className="cursor-pointer">
                Solve Challenge
              </Badge>
            </CardFooter>
          </Card>
        </div>
      </div>
    </>
  );
}
