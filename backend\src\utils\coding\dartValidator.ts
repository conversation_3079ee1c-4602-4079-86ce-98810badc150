/**
 * Utility to validate Dart code for common errors
 * This is used when Judge<PERSON> fails to properly handle Dart code
 */

interface DartValidationResult {
  isValid: boolean;
  errors: string[];
}

export const dartValidator = {
  /**
   * Validates Dart code for common syntax errors and typos
   * @param code The Dart code to validate
   * @returns Validation result with errors if any
   */
  validateDartCode(code: string): DartValidationResult {
    const errors: string[] = [];
    
    // Check for variable usage before declaration
    this.checkUndefinedVariables(code, errors);
    
    // Check for common typos
    this.checkCommonTypos(code, errors);
    
    // Check for mismatched brackets
    this.checkMismatchedBrackets(code, errors);
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },
  
  /**
   * Check for variables used before they are declared
   */
  checkUndefinedVariables(code: string, errors: string[]): void {
    // Extract all variable declarations
    const declarations: Record<string, boolean> = {};
    const declarationRegex = /\b(?:var|final|const)\s+(\w+)\b/g;
    let match;
    
    while ((match = declarationRegex.exec(code)) !== null) {
      declarations[match[1]] = true;
    }
    
    // Check for variable usages
    const lines = code.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith('//')) continue;
      
      // Check for variable usages in this line
      const usageMatches = line.match(/\b(\w+)\b/g) || [];
      
      for (const usage of usageMatches) {
        // Skip keywords, numbers, and common Dart identifiers
        if (this.isDartKeyword(usage) || /^\d+$/.test(usage) || 
            this.isCommonDartIdentifier(usage)) {
          continue;
        }
        
        // Check if the variable is used before declaration
        if (!declarations[usage] && 
            this.isLikelyVariable(usage, line) && 
            !this.isParameterOrImport(usage, code)) {
          
          // Check if it's a typo of another variable
          const similarVars = this.findSimilarVariables(usage, Object.keys(declarations));
          if (similarVars.length > 0) {
            errors.push(`Line ${i + 1}: Possible typo: '${usage}' is not defined. Did you mean '${similarVars[0]}'?`);
          } else {
            errors.push(`Line ${i + 1}: '${usage}' is used before it's declared.`);
          }
        }
      }
    }
  },
  
  /**
   * Check for common typos in Dart code
   */
  checkCommonTypos(code: string, errors: string[]): void {
    const typos: Record<string, string> = {
      'compokjlement': 'complement',
      'nolpums': 'nums',
      'lenght': 'length',
      'indicoles': 'indices',
      'containskey': 'containsKey',
      'Listfrom': 'List.from'
    };
    
    const lines = code.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith('//')) continue;
      
      for (const [typo, correction] of Object.entries(typos)) {
        if (line.includes(typo)) {
          errors.push(`Line ${i + 1}: Possible typo: '${typo}' should be '${correction}'.`);
        }
      }
    }
  },
  
  /**
   * Check for mismatched brackets
   */
  checkMismatchedBrackets(code: string, errors: string[]): void {
    const stack: { char: string, line: number }[] = [];
    const lines = code.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments
      if (line.trim().startsWith('//')) continue;
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        
        if (char === '{' || char === '[' || char === '(') {
          stack.push({ char, line: i + 1 });
        } else if (char === '}' || char === ']' || char === ')') {
          if (stack.length === 0) {
            errors.push(`Line ${i + 1}: Unexpected closing '${char}'.`);
            continue;
          }
          
          const last = stack.pop()!;
          const expected = this.getMatchingBracket(last.char);
          
          if (char !== expected) {
            errors.push(`Line ${i + 1}: Expected '${expected}' to match '${last.char}' from line ${last.line}, but found '${char}'.`);
          }
        }
      }
    }
    
    if (stack.length > 0) {
      for (const item of stack) {
        errors.push(`Line ${item.line}: Unclosed '${item.char}'.`);
      }
    }
  },
  
  /**
   * Get the matching closing bracket for an opening bracket
   */
  getMatchingBracket(char: string): string {
    switch (char) {
      case '{': return '}';
      case '[': return ']';
      case '(': return ')';
      default: return '';
    }
  },
  
  /**
   * Check if a string is a Dart keyword
   */
  isDartKeyword(word: string): boolean {
    const keywords = [
      'abstract', 'as', 'assert', 'async', 'await', 'break', 'case', 'catch',
      'class', 'const', 'continue', 'covariant', 'default', 'deferred', 'do',
      'dynamic', 'else', 'enum', 'export', 'extends', 'extension', 'external',
      'factory', 'false', 'final', 'finally', 'for', 'Function', 'get', 'hide',
      'if', 'implements', 'import', 'in', 'interface', 'is', 'late', 'library',
      'mixin', 'new', 'null', 'on', 'operator', 'part', 'required', 'rethrow',
      'return', 'set', 'show', 'static', 'super', 'switch', 'sync', 'this',
      'throw', 'true', 'try', 'typedef', 'var', 'void', 'while', 'with', 'yield',
      'print', 'main', 'List', 'Map', 'int', 'double', 'bool', 'String', 'dynamic',
      'from', 'length', 'target', 'input', 'solution', 'testCases', 'result'
    ];
    
    return keywords.includes(word);
  },
  
  /**
   * Check if a string is a common Dart identifier
   */
  isCommonDartIdentifier(word: string): boolean {
    const commonIdentifiers = [
      'i', 'j', 'k', 'n', 'x', 'y', 'z', 'a', 'b', 'c', 'e', 'f', 'g', 'h',
      'toString', 'hashCode', 'runtimeType', 'noSuchMethod', 'index', 'key',
      'value', 'add', 'remove', 'contains', 'isEmpty', 'isNotEmpty', 'clear',
      'forEach', 'map', 'where', 'any', 'every', 'fold', 'reduce', 'expand',
      'join', 'split', 'trim', 'toLowerCase', 'toUpperCase', 'substring',
      'indexOf', 'lastIndexOf', 'startsWith', 'endsWith', 'replaceAll',
      'replaceFirst', 'contains', 'containsKey', 'keys', 'values', 'entries',
      'addAll', 'putIfAbsent', 'remove', 'clear', 'from', 'of', 'generate',
      'filled', 'empty', 'unmodifiable', 'growable', 'fixed', 'shuffle',
      'sort', 'reversed', 'asMap', 'sublist', 'getRange', 'setRange',
      'removeRange', 'fillRange', 'replaceRange', 'insertAll', 'removeAt',
      'removeLast', 'removeWhere', 'retainWhere', 'indexOf', 'lastIndexOf',
      'elementAt', 'first', 'last', 'single', 'firstWhere', 'lastWhere',
      'singleWhere', 'take', 'takeWhile', 'skip', 'skipWhile', 'followedBy',
      'whereType', 'cast', 'toList', 'toSet', 'length'
    ];
    
    return commonIdentifiers.includes(word);
  },
  
  /**
   * Check if a word is likely a variable based on context
   */
  isLikelyVariable(word: string, line: string): boolean {
    // Check if it's used in a context that suggests it's a variable
    return !line.includes(`${word}(`) && // Not a function call
           !line.includes(`${word}.`) && // Not a class/namespace
           !line.includes(`.${word}`) && // Not a property access
           word.length > 1; // Not a single character (likely a loop variable)
  },
  
  /**
   * Check if a word is a parameter or import
   */
  isParameterOrImport(word: string, code: string): boolean {
    // Check if it's a function parameter
    const paramRegex = new RegExp(`\\(.*\\b${word}\\b.*\\)`, 'g');
    if (paramRegex.test(code)) return true;
    
    // Check if it's imported
    const importRegex = new RegExp(`import.*\\b${word}\\b`, 'g');
    return importRegex.test(code);
  },
  
  /**
   * Find similar variables to a potentially misspelled one
   */
  findSimilarVariables(word: string, variables: string[]): string[] {
    return variables.filter(variable => {
      // Simple similarity check - at least 60% of characters match
      let matches = 0;
      const minLength = Math.min(word.length, variable.length);
      const maxLength = Math.max(word.length, variable.length);
      
      for (let i = 0; i < minLength; i++) {
        if (word[i] === variable[i]) matches++;
      }
      
      return matches / maxLength > 0.6;
    });
  }
};
